# FAAFO Career Platform API Documentation

## Overview

The FAAFO Career Platform API provides endpoints for managing user authentication, career assessments, learning resources, progress tracking, salary calculations, and personalized recommendations.

## Base URL

```
https://your-domain.com/api
```

## Authentication

Most endpoints require authentication using NextAuth.js sessions. Include the session cookie in your requests.

## Rate Limiting

- General API: 100 requests per 15 minutes
- Authentication: 10 requests per 15 minutes
- Password Reset: 3 requests per hour
- Search: 200 requests per 15 minutes
- Write Operations: 50 requests per 15 minutes

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": {},
  "message": "Optional success message",
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "details": ["Detailed error messages"],
  "code": "ERROR_CODE"
}
```

## Endpoints

### Authentication

#### POST /api/signup
Create a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "StrongPassword123!"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user-id",
    "email": "<EMAIL>",
    "createdAt": "2024-01-01T00:00:00Z"
  },
  "message": "User created successfully"
}
```

#### POST /api/auth/forgot-password
Request a password reset.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

#### POST /api/auth/reset-password
Reset password with token.

**Request Body:**
```json
{
  "token": "reset-token",
  "password": "NewPassword123!"
}
```

### Assessments

#### GET /api/assessment
Get current user's assessment.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "assessment-id",
    "currentStep": 2,
    "status": "IN_PROGRESS",
    "formData": {},
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

#### POST /api/assessment
Save assessment progress.

**Request Body:**
```json
{
  "currentStep": 2,
  "formData": {
    "question1": "answer1",
    "question2": ["option1", "option2"]
  },
  "status": "IN_PROGRESS"
}
```

#### PUT /api/assessment
Submit completed assessment.

**Request Body:**
```json
{
  "assessmentId": "assessment-id",
  "formData": {}
}
```

### Assessment Data Validation

The assessment API includes comprehensive validation for all form data:

#### Question Key Validation
- Only valid question keys from the assessment definition are accepted
- Invalid question keys return a 400 error with details

#### Data Type Validation
- **Multiple Choice (Single)**: Must be a string with valid enum value
- **Multiple Choice (Multiple)**: Must be an array of valid enum values
- **Scale Questions**: Must be a number within the specified range (e.g., 1-5)
- **Text Questions**: Must be a string within length constraints

#### Example Validation Errors
```json
{
  "error": "Question 'work_environment_preference' expects a string value, but received: number."
}
```

```json
{
  "error": "Invalid option 'invalid_value' for question 'desired_outcomes_work_life'. Valid options: very_important, important, moderate, less_important, not_important."
}
```

```json
{
  "error": "Question 'dissatisfaction_triggers' expects an array of values, but received: string."
}
```

### Learning Resources

#### GET /api/learning-resources
Get paginated learning resources with filters.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10, max: 100)
- `category` (string): Filter by category
- `skillLevel` (string): Filter by skill level
- `type` (string): Filter by resource type
- `cost` (string): Filter by cost
- `search` (string): Search in title, description, author

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "resource-id",
      "title": "Resource Title",
      "description": "Resource description",
      "url": "https://example.com",
      "type": "COURSE",
      "category": "DATA_SCIENCE",
      "skillLevel": "BEGINNER",
      "cost": "FREE",
      "averageRating": 4.5,
      "totalRatings": 10,
      "careerPaths": [],
      "skills": []
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

#### POST /api/learning-resources
Create a new learning resource.

**Request Body:**
```json
{
  "title": "Resource Title",
  "description": "Resource description",
  "url": "https://example.com",
  "type": "COURSE",
  "category": "DATA_SCIENCE",
  "skillLevel": "BEGINNER",
  "format": "SELF_PACED",
  "author": "Author Name",
  "duration": "2 hours",
  "cost": "FREE"
}
```

#### GET /api/learning-resources/[id]
Get a specific learning resource.

#### PUT /api/learning-resources/[id]
Update a learning resource.

### Progress Tracking

#### GET /api/learning-progress
Get user's learning progress.

#### POST /api/learning-progress
Update learning progress.

**Request Body:**
```json
{
  "resourceId": "resource-id",
  "status": "COMPLETED",
  "notes": "Great resource!",
  "rating": 5,
  "review": "Very helpful for beginners"
}
```

#### DELETE /api/learning-progress
Delete progress record.

### Resource Ratings

#### GET /api/resource-ratings
Get ratings for a resource.

**Query Parameters:**
- `resourceId` (string): Resource ID

#### POST /api/resource-ratings
Submit a rating.

**Request Body:**
```json
{
  "resourceId": "resource-id",
  "rating": 5,
  "review": "Excellent resource",
  "isHelpful": true
}
```

### Career Suggestions

#### GET /api/career-suggestions
Get career path suggestions based on assessment.

**Query Parameters:**
- `assessmentId` (string): Assessment ID

### Personalized Resources

#### GET /api/personalized-resources
Get personalized learning recommendations.

**Query Parameters:**
- `limit` (number): Number of resources to return
- `skillLevel` (string): Preferred skill level

### Progress Tracker

#### GET /api/progress-tracker
Get comprehensive progress data.

#### POST /api/progress-tracker
Update weekly goal.

**Request Body:**
```json
{
  "weeklyGoal": 5
}
```

### Salary Calculator

#### GET /api/tools/salary-calculator
Get salary calculator data (career paths, locations, experience levels).

**Query Parameters:**
- `type` (string): Data type to retrieve
  - `career-paths`: Get all available career paths
  - `locations`: Get all supported locations
  - `experience-levels`: Get all experience levels

**Response (career-paths):**
```json
{
  "success": true,
  "data": [
    {
      "name": "AI/Machine Learning Engineer",
      "min": 95000,
      "max": 200000,
      "growth": "22.1%",
      "demand": "high",
      "skills": ["Python", "TensorFlow", "PyTorch", "Machine Learning", "Deep Learning"]
    }
  ]
}
```

**Response (locations):**
```json
{
  "success": true,
  "data": [
    {
      "name": "San Francisco, CA",
      "multiplier": 1.8,
      "category": "Very High Cost"
    }
  ]
}
```

#### POST /api/tools/salary-calculator
Calculate salary estimate based on provided parameters.

**Request Body:**
```json
{
  "careerPath": "Data Scientist",
  "experienceLevel": "senior",
  "location": "San Francisco, CA",
  "skills": ["Python", "Machine Learning"],
  "education": "master",
  "companySize": "large",
  "industry": "technology"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "baseRange": {
      "min": 80000,
      "max": 160000
    },
    "adjustedRange": {
      "min": 144000,
      "max": 288000
    },
    "median": 216000,
    "factors": {
      "location": {
        "multiplier": 1.8,
        "impact": "Positive"
      },
      "experience": {
        "multiplier": 1.3,
        "impact": "Strong"
      },
      "skills": {
        "bonus": 0.15,
        "impact": "Strong"
      },
      "education": {
        "multiplier": 1.1,
        "impact": "Positive"
      },
      "companySize": {
        "multiplier": 1.0,
        "impact": "Neutral"
      }
    },
    "confidence": 85,
    "dataPoints": 1000,
    "marketInsights": {
      "demand": "high",
      "growth": "11.5%",
      "topSkills": ["Python", "R", "SQL", "Machine Learning", "Statistics"],
      "recommendations": [
        "Negotiate based on total compensation",
        "Consider company equity and benefits"
      ]
    },
    "comparisons": {
      "percentile25": 180000,
      "percentile75": 252000,
      "nationalAverage": 120000
    }
  }
}
```

**Validation Rules:**
- `careerPath`: Must match available career paths
- `experienceLevel`: Must be one of: entry, junior, mid, senior, lead, principal, executive
- `location`: Must be a supported location or "Other"
- `skills`: Optional array of strings
- `education`: Must be one of: high_school, associate, bachelor, master, phd, bootcamp, self_taught
- `companySize`: Must be one of: startup, small, medium, large, enterprise
- `industry`: Optional string (default: "technology")

## Error Codes

- `UNAUTHORIZED` (401): Authentication required
- `FORBIDDEN` (403): Access denied
- `NOT_FOUND` (404): Resource not found
- `BAD_REQUEST` (400): Invalid request
- `VALIDATION_ERROR` (400): Input validation failed
- `CONFLICT` (409): Resource already exists
- `RATE_LIMIT_EXCEEDED` (429): Too many requests
- `INTERNAL_ERROR` (500): Server error
- `SERVICE_UNAVAILABLE` (503): Service temporarily unavailable

## Examples

### JavaScript/TypeScript

```typescript
// Fetch learning resources
const response = await fetch('/api/learning-resources?page=1&limit=10&category=DATA_SCIENCE');
const data = await response.json();

if (data.success) {
  console.log('Resources:', data.data);
  console.log('Total pages:', data.meta.totalPages);
} else {
  console.error('Error:', data.error);
}
```

### cURL

```bash
# Get learning resources
curl -X GET "https://your-domain.com/api/learning-resources?page=1&limit=10" \
  -H "Content-Type: application/json"

# Create a rating
curl -X POST "https://your-domain.com/api/resource-ratings" \
  -H "Content-Type: application/json" \
  -d '{
    "resourceId": "resource-id",
    "rating": 5,
    "review": "Great resource!"
  }'
```
