---
title: "Documentation Naming Conventions"
category: "atoms"
subcategory: "concepts"
tags: ["naming", "conventions", "files", "standards"]
last_updated: "2025-06-15"
last_validated: "2025-06-15"
dependencies: []
used_by: []
maintainer: "documentation-team"
ai_context: "File naming conventions for FAAFO Career Platform documentation"
---

## Documentation Naming Conventions

### **Prefixes by Type**
- `IMPLEMENTATION_*.md` - Implementation guides
- `TESTING_*.md` - Testing documentation
- `ASSESSMENT_*.md` - Assessment-related docs
- `DATABASE_*.md` - Database documentation
- `DEPLOYMENT_*.md` - Deployment guides
- `PHASE*_*.md` - Phase-specific documentation

### **Suffixes by Purpose**
- `*_GUIDE.md` - Step-by-step guides
- `*_SUMMARY.md` - Summary documents
- `*_REPORT.md` - Reports and analysis
- `*_PLAN.md` - Planning documents
- `*_STRATEGY.md` - Strategy documents

### **Special Files**
- `README.md` - Directory overview (one per directory)
- `00_PROJECT_OVERVIEW.md` - Project overview (numbered for ordering)
- `01_REQUIREMENTS.md` - Requirements (numbered for ordering)

### **Examples**
- ✅ `TESTING_STRATEGY.md`
- ✅ `IMPLEMENTATION_GUIDE.md`
- ✅ `ASSESSMENT_RESULTS_SUMMARY.md`
- ✅ `DATABASE_MIGRATION_PLAN.md`
- ❌ `test-stuff.md`
- ❌ `random-notes.md`
- ❌ `temp-doc.md`

### **Quick Check**
Before creating any .md file, ask:
1. What category does this belong to?
2. Is there an existing file I should update instead?
3. Does this belong in the correct `docs/` subdirectory?
4. Does the filename follow our conventions?
