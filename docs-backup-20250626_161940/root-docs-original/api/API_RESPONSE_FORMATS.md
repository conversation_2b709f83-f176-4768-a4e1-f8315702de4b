# API Response Formats - Skill Gap Analyzer

## 🎯 Overview

This document defines the standardized response formats for all Skill Gap Analyzer API endpoints, including EdgeCaseHandler integration patterns.

## 📋 Standard Response Structure

### Success Response Format

```typescript
interface StandardSuccessResponse<T> {
  success: true;
  data: T;
  metadata?: {
    cached?: boolean;
    generatedAt: string;
    edgeCaseHandlerUsed?: boolean;
    edgeCaseHandlerData?: EdgeCaseHandlerMetadata;
    performanceMetrics?: PerformanceMetrics;
  };
  message?: string;
}
```

### Error Response Format

```typescript
interface StandardErrorResponse {
  success: false;
  error: string;
  errorType?: 'VALIDATION_ERROR' | 'AI_SERVICE_ERROR' | 'DATABASE_ERROR' | 'CIRCUIT_BREAKER_OPEN' | 'RATE_LIMIT_EXCEEDED';
  details?: any;
  fallbackData?: any;
  suggestedAlternatives?: string[];
  retryable?: boolean;
  retryAfter?: number; // seconds
  errorCode?: string;
  timestamp: string;
}
```

## 🔌 Endpoint-Specific Formats

### 1. Skills Analysis Endpoint

**POST** `/api/ai/skills-analysis`

#### Success Response
```json
{
  "success": true,
  "data": {
    "analysisId": "550e8400-e29b-41d4-a716-446655440000",
    "skillGaps": [
      {
        "skillId": "react-advanced",
        "skillName": "Advanced React",
        "currentLevel": 3,
        "requiredLevel": 8,
        "gapSeverity": "HIGH",
        "marketDemand": "HIGH",
        "learningPriority": 1,
        "estimatedLearningTime": 120,
        "description": "Advanced React patterns and optimization techniques"
      }
    ],
    "learningPlan": {
      "totalEstimatedHours": 240,
      "phases": [
        {
          "phase": 1,
          "title": "Foundation Building",
          "duration": 80,
          "skills": ["react-hooks", "state-management"],
          "milestones": ["Complete React hooks course", "Build state management project"]
        }
      ],
      "estimatedDuration": 6,
      "milestones": ["Phase 1 Complete", "Phase 2 Complete"],
      "recommendedResources": [
        {
          "title": "Advanced React Course",
          "type": "course",
          "url": "https://example.com/react-course",
          "estimatedHours": 40,
          "difficulty": "intermediate"
        }
      ]
    },
    "careerReadiness": {
      "currentScore": 65,
      "targetScore": 90,
      "improvementPotential": 25,
      "timeToTarget": 6,
      "readinessLevel": "INTERMEDIATE",
      "strengthAreas": ["Frontend Development", "JavaScript"],
      "improvementAreas": ["System Design", "Backend Integration"]
    },
    "marketInsights": {
      "skillDemandTrends": {
        "react": { "trend": "increasing", "demandScore": 95 },
        "typescript": { "trend": "stable", "demandScore": 88 }
      },
      "salaryProjections": {
        "currentRange": { "min": 70000, "max": 90000 },
        "targetRange": { "min": 100000, "max": 130000 }
      },
      "jobMarketData": {
        "openPositions": 1250,
        "competitionLevel": "moderate",
        "hiringTrends": "positive"
      }
    }
  },
  "metadata": {
    "cached": false,
    "generatedAt": "2025-01-01T12:00:00Z",
    "edgeCaseHandlerUsed": true,
    "edgeCaseHandlerData": {
      "sanitizedInput": true,
      "isNewUser": false,
      "retryCount": 0,
      "fallbackDataUsed": false
    },
    "performanceMetrics": {
      "responseTime": 2340,
      "aiServiceTime": 1800,
      "databaseTime": 120
    }
  },
  "message": "Skills analysis completed successfully"
}
```

#### Error Response
```json
{
  "success": false,
  "error": "AI service temporarily unavailable",
  "errorType": "AI_SERVICE_ERROR",
  "fallbackData": {
    "skillGaps": [],
    "learningPlan": {
      "totalEstimatedHours": 0,
      "phases": [],
      "estimatedDuration": 0,
      "milestones": [],
      "recommendedResources": []
    },
    "careerReadiness": {
      "currentScore": 0,
      "targetScore": 100,
      "improvementPotential": 100,
      "timeToTarget": 12,
      "readinessLevel": "BEGINNER",
      "strengthAreas": [],
      "improvementAreas": []
    }
  },
  "suggestedAlternatives": [
    "Try again in a few minutes",
    "Use basic skill assessment",
    "Contact support if issue persists"
  ],
  "retryable": true,
  "retryAfter": 300,
  "errorCode": "AI_SERVICE_TIMEOUT",
  "timestamp": "2025-01-01T12:00:00Z"
}
```

### 2. Comprehensive Skills Analysis

**POST** `/api/ai/skills-analysis/comprehensive`

#### Success Response
```json
{
  "success": true,
  "data": {
    "analysisId": "550e8400-e29b-41d4-a716-446655440001",
    "skillGaps": [/* detailed skill gaps */],
    "learningPlan": {
      "totalEstimatedHours": 320,
      "phases": [/* detailed phases */],
      "milestones": [/* comprehensive milestones */],
      "recommendedResources": [/* curated resources */],
      "personalizedPath": {
        "learningStyle": "visual",
        "preferredPace": "moderate",
        "availableHours": 10,
        "customRecommendations": [/* personalized suggestions */]
      }
    },
    "careerReadiness": {/* enhanced readiness data */},
    "marketInsights": {/* comprehensive market data */},
    "progressTracking": {
      "milestones": [/* trackable milestones */],
      "completedMilestones": [],
      "currentPhase": "planning",
      "nextSteps": [/* immediate action items */]
    }
  },
  "metadata": {
    "cached": false,
    "generatedAt": "2025-01-01T12:00:00Z",
    "edgeCaseHandlerUsed": true,
    "analysisType": "comprehensive",
    "processingTime": 4200
  }
}
```

### 3. Skill Assessment Endpoints

**POST** `/api/skills/assessment`

#### Success Response
```json
{
  "success": true,
  "data": {
    "assessmentId": "550e8400-e29b-41d4-a716-446655440002",
    "userId": "user123",
    "assessments": [
      {
        "skillName": "React",
        "selfRating": 7,
        "confidenceLevel": 8,
        "yearsOfExperience": 3,
        "lastUsed": "2024-12-01",
        "certifications": ["React Developer Certification"],
        "projects": ["E-commerce Platform", "Dashboard Application"]
      }
    ],
    "summary": {
      "totalSkills": 15,
      "averageRating": 6.8,
      "strongestAreas": ["Frontend Development", "JavaScript"],
      "improvementAreas": ["Backend Development", "DevOps"]
    }
  },
  "metadata": {
    "generatedAt": "2025-01-01T12:00:00Z",
    "cached": false
  }
}
```

## 🔄 EdgeCaseHandler Response Patterns

### Fallback Data Structure

```typescript
interface FallbackData {
  skillGaps: SkillGap[];
  learningPlan: {
    totalEstimatedHours: number;
    phases: LearningPhase[];
    estimatedDuration: number;
    milestones: string[];
    recommendedResources: Resource[];
  };
  careerReadiness: {
    currentScore: number;
    targetScore: number;
    improvementPotential: number;
    timeToTarget: number;
    readinessLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
    strengthAreas: string[];
    improvementAreas: string[];
  };
}
```

### EdgeCaseHandler Metadata

```typescript
interface EdgeCaseHandlerMetadata {
  sanitizedInput?: boolean;
  isNewUser?: boolean;
  onboardingRecommendations?: string[];
  retryCount?: number;
  fallbackDataUsed?: boolean;
  suggestedAlternatives?: string[];
  circuitBreakerState?: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
  lastSuccessfulCall?: string;
}
```

## 🚨 Error Handling Patterns

### Validation Errors (400)
```json
{
  "success": false,
  "error": "Invalid request data",
  "errorType": "VALIDATION_ERROR",
  "details": [
    {
      "field": "currentSkills",
      "message": "At least one skill is required",
      "code": "REQUIRED_FIELD"
    }
  ],
  "timestamp": "2025-01-01T12:00:00Z"
}
```

### Rate Limiting (429)
```json
{
  "success": false,
  "error": "Rate limit exceeded",
  "errorType": "RATE_LIMIT_EXCEEDED",
  "retryAfter": 900,
  "details": {
    "limit": 5,
    "window": "15 minutes",
    "remaining": 0,
    "resetTime": "2025-01-01T12:15:00Z"
  },
  "timestamp": "2025-01-01T12:00:00Z"
}
```

### Service Unavailable (503)
```json
{
  "success": false,
  "error": "Service temporarily unavailable",
  "errorType": "AI_SERVICE_ERROR",
  "fallbackData": {/* minimal fallback data */},
  "suggestedAlternatives": [
    "Try again in a few minutes",
    "Use cached results if available",
    "Contact support for assistance"
  ],
  "retryable": true,
  "retryAfter": 300,
  "timestamp": "2025-01-01T12:00:00Z"
}
```

## 📊 Performance Metrics

### Response Time Tracking
```typescript
interface PerformanceMetrics {
  responseTime: number;        // Total response time in ms
  aiServiceTime?: number;      // AI service processing time
  databaseTime?: number;       // Database query time
  cacheTime?: number;          // Cache lookup time
  edgeCaseHandlerTime?: number; // EdgeCaseHandler processing time
}
```

## 🔧 Client Integration Examples

### JavaScript/TypeScript Client

```typescript
// Handle API response with EdgeCaseHandler support
async function handleSkillsAnalysis(response: Response) {
  const data = await response.json();
  
  if (data.success) {
    // Handle successful response
    if (data.metadata?.edgeCaseHandlerData?.fallbackDataUsed) {
      showWarning('Some services were unavailable. Results may be limited.');
    }
    return data.data;
  } else {
    // Handle error with fallback data
    if (data.fallbackData) {
      showWarning(`${data.error}. Showing limited results.`);
      return data.fallbackData;
    }
    
    // Show suggested alternatives
    if (data.suggestedAlternatives) {
      showAlternatives(data.suggestedAlternatives);
    }
    
    throw new Error(data.error);
  }
}
```

### React Component Integration

```tsx
// Component handling EdgeCaseHandler responses
function SkillAnalysisResults({ analysisData, edgeCaseHandlerData }) {
  return (
    <div>
      {edgeCaseHandlerData?.fallbackDataUsed && (
        <Alert variant="warning">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Limited Results</AlertTitle>
          <AlertDescription>
            Some services were unavailable. Results may be incomplete.
          </AlertDescription>
        </Alert>
      )}
      
      {/* Render analysis results */}
      <SkillGapDisplay data={analysisData} />
    </div>
  );
}
```

## 📚 Related Documentation

- [EdgeCaseHandler Integration Guide](../integration/EDGE_CASE_HANDLER_INTEGRATION.md)
- [Error Handling Best Practices](../development/ERROR_HANDLING.md)
- [Performance Monitoring](../monitoring/PERFORMANCE_MONITORING.md)
- [API Authentication](../api/AUTHENTICATION.md)
