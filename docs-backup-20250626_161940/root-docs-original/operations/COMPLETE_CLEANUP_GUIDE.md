# Complete Documentation Cleanup Guide

This guide helps you find and consolidate ALL scattered documentation files across your entire project.

## 🔍 Known Scattered Documentation Locations

Based on analysis, documentation files may be scattered in these locations:

### Root Level Files
- `README.md` (multiple versions)
- `DOCUMENTATION_INDEX.md`
- Any `*.md` files in project root

### Scattered Directories
- `/docs` (at project root)
- `/project-docs`
- `/__tests__` (with documentation)
- `/scripts` (with documentation)

### Specific Files Mentioned
- `TESTING_GUIDE.md`
- `testing-strategy.md`
- Various test reports and guides

## 🛠️ Automated Cleanup Script

We've created a comprehensive cleanup script that will:

1. **Find all scattered documentation files**
2. **Create backups of everything**
3. **Consolidate into organized structure**
4. **Generate a detailed report**

### Running the Cleanup Script

```bash
# Navigate to your project root
cd /path/to/your/faafo/project

# Run the consolidation script
./faafo-career-platform/scripts/consolidate-documentation.sh
```

The script will:
- ✅ Search your entire project for documentation files
- ✅ Create timestamped backups
- ✅ Move files to appropriate categories
- ✅ Remove empty directories
- ✅ Generate a consolidation report

## 📋 Manual Cleanup Checklist

If you prefer to clean up manually, use this checklist:

### Step 1: Find All Documentation Files

```bash
# Find all markdown files (excluding node_modules)
find . -name "*.md" -not -path "*/node_modules/*" | sort

# Find documentation directories
find . -type d -name "*doc*" -o -name "*test*" | grep -v node_modules

# Find specific files
find . -name "*guide*" -o -name "*strategy*" -o -name "*index*" | grep -v node_modules
```

### Step 2: Categorize and Move Files

**Architecture Documentation** → `faafo-career-platform/docs/architecture/`
- Project overviews
- Requirements documents
- Architecture diagrams
- Technical specifications
- UX guidelines
- Data policies

**Development Documentation** → `faafo-career-platform/docs/development/`
- Setup guides
- API documentation
- Database schemas
- Contributing guidelines

**User Documentation** → `faafo-career-platform/docs/user/`
- User guides
- FAQ documents
- Troubleshooting guides

**Testing Documentation** → `faafo-career-platform/docs/testing/`
- Testing strategies
- Test reports
- Testing guides

**Archive** → `faafo-career-platform/docs/archive/`
- Outdated documentation
- Duplicate files
- Legacy documents

### Step 3: Remove Empty Directories

```bash
# Find and remove empty directories
find . -type d -empty -not -path "*/node_modules/*" -not -path "*/.git/*"
```

### Step 4: Update Links

After moving files, update any internal links:

```bash
# Search for broken internal links
grep -r "\[.*\](\./" faafo-career-platform/docs/
```

## 🎯 Target Documentation Structure

After cleanup, your documentation should look like this:

```
faafo-career-platform/docs/
├── README.md                           # Main documentation index
├── COMPLETE_CLEANUP_GUIDE.md          # This guide
├── CONSOLIDATION_REPORT.md             # Generated cleanup report
├── user/                               # End-user documentation
│   ├── user-guide.md
│   ├── faq-troubleshooting.md
│   └── troubleshooting-guide.md
├── development/                        # Developer documentation
│   ├── setup.md
│   ├── api.md
│   ├── database.md
│   └── contributing.md
├── architecture/                       # System design documentation
│   ├── overview.md
│   ├── architecture.md
│   ├── requirements.md
│   ├── tech-specs.md
│   ├── ux-guidelines.md
│   ├── data-policy.md
│   ├── assessment-system.md
│   └── glossary.md
├── testing/                           # Testing documentation
│   ├── testing-guide.md
│   └── reports/                       # Test execution reports
│       ├── DASHBOARD_TEST_REPORT.md
│       ├── IMPLEMENTATION_TEST_REPORT.md
│       └── ASSESSMENT_IMPROVEMENTS_SUMMARY.md
└── archive/                           # Archived/legacy documentation
    └── (moved legacy files)
```

## 🔍 Verification Steps

After cleanup, verify everything is properly organized:

### 1. Check Documentation Structure

```bash
# List all documentation files
find faafo-career-platform/docs -name "*.md" | sort

# Verify no scattered files remain
find . -name "*.md" -not -path "*/faafo-career-platform/docs/*" -not -path "*/node_modules/*"
```

### 2. Test Documentation Links

```bash
# Check for broken internal links
grep -r "\[.*\](\." faafo-career-platform/docs/

# Verify all referenced files exist
```

### 3. Update Main README

Ensure your main README.md files point to the correct documentation locations.

## 🚨 Common Issues and Solutions

### Issue: Files Not Found
**Solution**: The files might be in a different location or already moved. Use the find commands to locate them.

### Issue: Permission Denied
**Solution**: Make sure you have write permissions to the directories.

```bash
# Fix permissions if needed
chmod -R u+w faafo-career-platform/docs/
```

### Issue: Broken Links After Moving
**Solution**: Update all internal documentation links to use the new paths.

### Issue: Duplicate Content
**Solution**: Compare files and merge or remove duplicates as appropriate.

## 📞 Getting Help

If you encounter issues during cleanup:

1. **Check the backup directory** - All original files are preserved
2. **Review the consolidation report** - Shows what was moved where
3. **Use git to track changes** - See exactly what was modified
4. **Restore from backup if needed** - Copy files back from backup directory

## 🎉 Post-Cleanup Tasks

After successful cleanup:

1. **Update development workflow** to use new documentation structure
2. **Update CI/CD scripts** that reference documentation paths
3. **Inform team members** about the new documentation organization
4. **Remove backup directory** once you're satisfied with the cleanup
5. **Update bookmarks** and documentation links in other tools

## 📈 Maintaining Clean Documentation

To prevent documentation scatter in the future:

1. **Use the organized structure** for all new documentation
2. **Review PRs** to ensure documentation goes in the right place
3. **Regular cleanup** - Run the consolidation script periodically
4. **Document the process** - Keep this guide updated
5. **Train team members** on the documentation structure

---

**Remember**: The automated script creates backups of everything, so you can always restore if something goes wrong!
