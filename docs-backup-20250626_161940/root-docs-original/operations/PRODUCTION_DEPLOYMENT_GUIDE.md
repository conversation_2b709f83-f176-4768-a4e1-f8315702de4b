# Production Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying the FAAFO Career Platform to production.

## Prerequisites

### System Requirements
- Node.js 18+ 
- PostgreSQL database (Neon recommended)
- Redis instance (for caching)
- SSL certificate
- Domain name configured

### Required Services
- **Database**: Neon PostgreSQL
- **Email**: Resend API
- **AI Service**: Google Gemini API
- **Monitoring**: Sentry
- **Caching**: Redis

## Pre-Deployment Checklist

### 1. Run Production Readiness Check
```bash
node scripts/production-readiness-check.js
```

### 2. Environment Variables
Create `.env.production` with the following variables:

```env
# Database
DATABASE_URL="postgresql://username:password@host:port/database?sslmode=require"

# NextAuth
NEXTAUTH_URL="https://yourdomain.com"
NEXTAUTH_SECRET="your-nextauth-secret-key"

# AI Services
GOOGLE_GEMINI_API_KEY="your-gemini-api-key"

# Email Service
RESEND_API_KEY="your-resend-api-key"

# Monitoring
SENTRY_DSN="your-sentry-dsn"

# Redis (if using)
REDIS_URL="redis://username:password@host:port"

# Security
CSRF_SECRET="your-csrf-secret"

# Application
NODE_ENV="production"
```

### 3. Build and Test
```bash
# Install dependencies
npm ci

# Run production build
npm run build

# Test the build
npm start
```

## Deployment Steps

### Step 1: Database Setup

1. **Create Production Database**
   ```bash
   # If using Neon, create database through their dashboard
   # Or use your preferred PostgreSQL provider
   ```

2. **Run Migrations**
   ```bash
   npx prisma migrate deploy
   ```

3. **Seed Initial Data** (if needed)
   ```bash
   npx prisma db seed
   ```

### Step 2: Application Deployment

#### Option A: Vercel Deployment (Recommended)

1. **Install Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **Deploy to Vercel**
   ```bash
   vercel --prod
   ```

3. **Configure Environment Variables**
   - Go to Vercel dashboard
   - Add all environment variables from `.env.production`
   - Redeploy if needed

#### Option B: Docker Deployment

1. **Build Docker Image**
   ```bash
   docker build -t faafo-career-platform .
   ```

2. **Run Container**
   ```bash
   docker run -p 3000:3000 \
     --env-file .env.production \
     faafo-career-platform
   ```

#### Option C: Traditional Server Deployment

1. **Upload Files**
   ```bash
   # Upload all files to your server
   rsync -avz --exclude node_modules . user@server:/path/to/app/
   ```

2. **Install Dependencies**
   ```bash
   npm ci --production
   ```

3. **Build Application**
   ```bash
   npm run build
   ```

4. **Start with PM2**
   ```bash
   pm2 start npm --name "faafo-career" -- start
   pm2 save
   pm2 startup
   ```

### Step 3: SSL and Domain Configuration

1. **Configure SSL Certificate**
   - Use Let's Encrypt for free SSL
   - Or upload your SSL certificate

2. **Configure Reverse Proxy** (if using Nginx)
   ```nginx
   server {
       listen 443 ssl;
       server_name yourdomain.com;
       
       ssl_certificate /path/to/certificate.crt;
       ssl_certificate_key /path/to/private.key;
       
       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

### Step 4: Monitoring and Logging

1. **Configure Sentry**
   - Verify Sentry DSN is set
   - Test error reporting

2. **Set Up Log Monitoring**
   ```bash
   # If using PM2
   pm2 logs faafo-career
   
   # Set up log rotation
   pm2 install pm2-logrotate
   ```

3. **Health Check Endpoint**
   - Verify `/api/health` endpoint works
   - Set up uptime monitoring

## Post-Deployment Verification

### 1. Smoke Tests
Run these tests immediately after deployment:

```bash
# Test homepage
curl -I https://yourdomain.com

# Test API health
curl https://yourdomain.com/api/health

# Test authentication
curl -X POST https://yourdomain.com/api/auth/signin

# Test database connection
curl https://yourdomain.com/api/career-paths
```

### 2. Feature Testing
- [ ] User registration and login
- [ ] Career assessment functionality
- [ ] Interview practice system
- [ ] Resume builder
- [ ] Career paths display
- [ ] Progress tracking
- [ ] Email notifications

### 3. Performance Testing
- [ ] Page load times < 3 seconds
- [ ] API response times < 2 seconds
- [ ] Database query performance
- [ ] Memory usage within limits

### 4. Security Testing
- [ ] HTTPS enforced
- [ ] CSRF protection active
- [ ] XSS protection working
- [ ] Input validation functioning
- [ ] Authentication flows secure

## Monitoring and Maintenance

### Daily Checks
- [ ] Error rates in Sentry
- [ ] Application uptime
- [ ] Database performance
- [ ] User activity levels

### Weekly Checks
- [ ] Security updates available
- [ ] Performance metrics review
- [ ] User feedback analysis
- [ ] Backup verification

### Monthly Checks
- [ ] Dependency updates
- [ ] Security audit
- [ ] Performance optimization
- [ ] Feature usage analytics

## Rollback Procedures

### Quick Rollback (Vercel)
```bash
vercel rollback [deployment-url]
```

### Manual Rollback
1. **Stop Current Application**
   ```bash
   pm2 stop faafo-career
   ```

2. **Restore Previous Version**
   ```bash
   git checkout [previous-commit]
   npm ci
   npm run build
   ```

3. **Restart Application**
   ```bash
   pm2 restart faafo-career
   ```

### Database Rollback
```bash
# Restore from backup
pg_restore -d database_name backup_file.sql

# Or run reverse migration
npx prisma migrate reset
```

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Node.js version compatibility
   - Verify all dependencies are installed
   - Check TypeScript compilation errors

2. **Database Connection Issues**
   - Verify DATABASE_URL format
   - Check network connectivity
   - Confirm SSL requirements

3. **Authentication Problems**
   - Verify NEXTAUTH_URL matches domain
   - Check NEXTAUTH_SECRET is set
   - Confirm callback URLs are correct

4. **Performance Issues**
   - Check database query performance
   - Verify caching is working
   - Monitor memory usage

### Support Contacts
- **Technical Issues**: [Your technical team]
- **Infrastructure**: [Your DevOps team]
- **Security**: [Your security team]

## Security Considerations

### Production Security Checklist
- [ ] All secrets stored securely
- [ ] HTTPS enforced everywhere
- [ ] CORS properly configured
- [ ] Rate limiting enabled
- [ ] Input validation active
- [ ] Error messages don't leak information
- [ ] Database access restricted
- [ ] Regular security updates applied

### Backup Strategy
- **Database**: Daily automated backups
- **Application**: Version control with Git
- **Environment**: Configuration backed up securely
- **Recovery**: Tested restore procedures

---

## Deployment Completion

Once deployment is complete:

1. **Update DNS** (if needed)
2. **Notify stakeholders**
3. **Monitor for 24 hours**
4. **Document any issues**
5. **Plan next release cycle**

**Deployment Status**: ✅ Ready for Production

For questions or issues, refer to the troubleshooting section or contact the technical team.
