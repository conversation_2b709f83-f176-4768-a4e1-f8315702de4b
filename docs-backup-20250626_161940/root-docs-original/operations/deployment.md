# Deployment Guide

This document provides comprehensive deployment procedures for the FAAFO Career Platform across different environments.

## 🎯 Overview

The platform uses a multi-environment deployment strategy with automated CI/CD pipelines for consistent and reliable deployments.

## 🏗️ Environment Structure

### Development Environment
- **Purpose**: Local development and testing
- **Database**: Vercel <PERSON> (Neon) - `neondb` ✅ **ACTIVE**
- **Authentication**: Development OAuth providers
- **Monitoring**: Basic logging

### Staging Environment
- **Purpose**: Pre-production testing and validation
- **Database**: Staging PostgreSQL database
- **Authentication**: Staging OAuth providers
- **Monitoring**: Full monitoring stack
- **URL**: `https://staging.faafo-career.com`

### Production Environment
- **Purpose**: Live user-facing application
- **Database**: Production PostgreSQL cluster
- **Authentication**: Production OAuth providers
- **Monitoring**: Comprehensive monitoring and alerting
- **URL**: `https://faafo-career.com`

## 🔄 Deployment Process

### 1. Pre-Deployment Checklist
- [ ] Code review completed and approved
- [ ] All tests passing (unit, integration, e2e)
- [ ] Database migrations tested
- [ ] Environment variables configured
- [ ] Security scan completed
- [ ] Performance impact assessed
- [ ] Rollback plan prepared

### 2. Staging Deployment
```bash
# 1. Deploy to staging
git checkout main
git pull origin main
npm run deploy:staging

# 2. Run smoke tests
npm run test:staging

# 3. Verify functionality
npm run verify:staging
```

### 3. Production Deployment
```bash
# 1. Create release tag
git tag -a v1.x.x -m "Release version 1.x.x"
git push origin v1.x.x

# 2. Deploy to production
npm run deploy:production

# 3. Run post-deployment verification
npm run verify:production
```

## 🛠️ Deployment Commands

### Local Development Setup
```bash
# Clone repository
git clone https://github.com/dm601990/faafo.git
cd faafo/faafo-career-platform

# Install dependencies
npm install

# Setup environment
cp .env.example .env.local
# Edit .env.local with local configuration

# Setup database
npx prisma migrate dev
npx prisma db seed

# Start development server
npm run dev
```

### Environment-Specific Deployments

#### Staging Deployment
```bash
# Build application
npm run build

# Run database migrations
npx prisma migrate deploy

# Deploy to staging infrastructure
npm run deploy:staging

# Verify deployment
curl -f https://staging.faafo-career.com/api/health
```

#### Production Deployment
```bash
# Build optimized application
npm run build:production

# Run database migrations (with backup)
npm run backup:database
npx prisma migrate deploy

# Deploy with zero-downtime
npm run deploy:production:rolling

# Verify deployment
npm run verify:production:health
```

## 🔧 Infrastructure Configuration

### Required Services
- **Application Server**: Node.js runtime environment
- **Database**: PostgreSQL 14+ with connection pooling
- **Cache**: Redis for session and application caching
- **File Storage**: Cloud storage for user uploads
- **CDN**: Content delivery network for static assets
- **Load Balancer**: For high availability and scaling

### Environment Variables

#### Core Configuration
```bash
# Application
NODE_ENV=production
PORT=3000
NEXTAUTH_URL=https://faafo-career.com
NEXTAUTH_SECRET=your-secret-key

# Database - Vercel Postgres (Neon) ✅ ACTIVE
DATABASE_URL=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require
POSTGRES_USER=neondb_owner
POSTGRES_PASSWORD=npg_BqdyWAlV08jN
POSTGRES_DB=neondb
POSTGRES_HOST=ep-cold-violet-a4fdonpt-pooler.us-east-1.aws.neon.tech
POSTGRES_PORT=5432

# Authentication
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Email
RESEND_API_KEY=your-resend-api-key
EMAIL_FROM=<EMAIL>

# Monitoring
SENTRY_DSN=your-sentry-dsn
SENTRY_ORG=your-sentry-org
SENTRY_PROJECT=your-sentry-project
```

#### Security Configuration
```bash
# Security
ENCRYPTION_KEY=your-encryption-key
JWT_SECRET=your-jwt-secret
CSRF_SECRET=your-csrf-secret

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# CORS
ALLOWED_ORIGINS=https://faafo-career.com,https://www.faafo-career.com
```

## 📊 Deployment Monitoring

### Health Checks
```bash
# Application health
curl -f https://faafo-career.com/api/health

# Database connectivity
curl -f https://faafo-career.com/api/health/database

# Authentication service
curl -f https://faafo-career.com/api/health/auth

# External services
curl -f https://faafo-career.com/api/health/external
```

### Performance Monitoring
- **Response Times**: Monitor API and page load times
- **Error Rates**: Track 4xx and 5xx error percentages
- **Throughput**: Monitor requests per second
- **Resource Usage**: CPU, memory, and disk utilization

### Deployment Metrics
- **Deployment Frequency**: Track deployment cadence
- **Lead Time**: Time from commit to production
- **Mean Time to Recovery**: Recovery time from failures
- **Change Failure Rate**: Percentage of deployments causing issues

## 🚨 Rollback Procedures

### Automatic Rollback Triggers
- Health check failures
- Error rate exceeding threshold
- Performance degradation
- Critical security alerts

### Manual Rollback Process
```bash
# 1. Identify last known good version
git log --oneline -10

# 2. Rollback application
npm run rollback:production --version=v1.x.x

# 3. Rollback database (if needed)
npx prisma migrate reset --to-migration=migration_name

# 4. Verify rollback
npm run verify:production
```

### Emergency Rollback
```bash
# Immediate rollback to previous version
npm run emergency:rollback

# Verify system stability
npm run verify:emergency
```

## 🔒 Security Considerations

### Deployment Security
- **Secure Credentials**: Use secure credential management
- **Network Security**: Deploy through secure networks
- **Access Control**: Limit deployment access to authorized personnel
- **Audit Logging**: Log all deployment activities

### Runtime Security
- **HTTPS**: Enforce HTTPS for all connections
- **Security Headers**: Implement security headers
- **Input Validation**: Validate all user inputs
- **Authentication**: Secure authentication mechanisms

## 📋 Post-Deployment Tasks

### Immediate Verification (0-15 minutes)
- [ ] Health checks passing
- [ ] Core functionality working
- [ ] Authentication working
- [ ] Database connectivity confirmed
- [ ] No critical errors in logs

### Extended Verification (15-60 minutes)
- [ ] Performance metrics within normal range
- [ ] User workflows functioning
- [ ] Email notifications working
- [ ] Background jobs processing
- [ ] Monitoring alerts configured

### Long-term Monitoring (1-24 hours)
- [ ] User feedback monitoring
- [ ] Performance trend analysis
- [ ] Error rate monitoring
- [ ] Resource utilization tracking
- [ ] Business metrics validation

## 📞 Support and Escalation

### Deployment Issues
- **Level 1**: Development team
- **Level 2**: DevOps team
- **Level 3**: Infrastructure team
- **Emergency**: On-call engineer

### Communication Channels
- **Internal**: Team chat and email
- **External**: Status page updates
- **Stakeholders**: Executive notifications
- **Users**: In-app notifications

---

[← Back to Operations Documentation](./README.md) | [← Back to Main Documentation](../README.md)
