# EdgeCaseHandler Integration Guide

## 🎯 Overview

The EdgeCaseHandler is a comprehensive error handling and recovery system integrated throughout the Skill Gap Analyzer. It provides production-ready error recovery, security protection, input validation, and graceful degradation with 91% test coverage.

## 🏗️ Architecture

### Core Components

1. **EdgeCaseHandlerService** (`/lib/services/edgeCaseHandlerService.ts`)
   - Main service class handling all edge cases
   - Provides fallback data and recovery strategies
   - Integrates with AI services, database operations, and user interactions

2. **Integration Points**
   - API Routes: `/api/ai/skills-analysis/*`
   - Frontend Components: `SkillGapAnalysis`, `SkillAssessmentForm`
   - Services: `GeminiService`, `SkillMarketDataService`, `PersonalizedLearningPathService`

## 🔌 API Integration

### Skills Analysis Endpoint

**Endpoint**: `POST /api/ai/skills-analysis/route.ts`

```typescript
// EdgeCaseHandler integration in API route
const edgeCaseResult = await edgeCaseHandlerService.handleAISkillsAnalysis({
  userId,
  currentSkills: allCurrentSkills,
  targetCareerPath,
  experienceLevel,
  timeframe,
  focusAreas,
  includeMarketData,
  careerPathData
});

if (edgeCaseResult.success) {
  analysisResult = { success: true, data: edgeCaseResult.data };
  edgeCaseHandlerUsed = true;
} else {
  // Fallback to direct AI service
  console.warn('EdgeCaseHandler failed, using fallback:', edgeCaseResult.error);
  analysisResult = await geminiService.analyzeSkillsGap(/* params */);
}
```

### Comprehensive Analysis Endpoint

**Endpoint**: `POST /api/ai/skills-analysis/comprehensive/route.ts`

```typescript
// EdgeCaseHandler for learning path generation
const edgeCaseResult = await edgeCaseHandlerService.handleLearningPathGeneration({
  userId,
  currentSkills: uniqueSkills.map(skill => ({
    skill: skill.skillName,
    level: skill.selfRating,
    confidence: skill.confidenceLevel
  })),
  targetCareerPath: requestData.targetCareerPath,
  preferences: requestData.preferences,
  careerPathData
});
```

## 📊 Response Format Changes

### Standard Success Response
```json
{
  "success": true,
  "data": {
    "analysisId": "uuid",
    "skillGaps": [...],
    "learningPlan": {...},
    "careerReadiness": {...}
  },
  "metadata": {
    "edgeCaseHandlerUsed": true,
    "cached": false,
    "generatedAt": "2025-01-01T00:00:00Z"
  }
}
```

### EdgeCaseHandler Error Response
```json
{
  "success": false,
  "error": "AI service temporarily unavailable",
  "errorType": "AI_SERVICE_ERROR",
  "fallbackData": {
    "skillGaps": [],
    "learningPlan": { "phases": [], "estimatedDuration": 0 },
    "careerReadiness": { "currentScore": 0, "targetScore": 100 }
  },
  "suggestedAlternatives": ["Try again in a few minutes", "Use basic analysis"],
  "retryable": true,
  "retryAfter": 300
}
```

## 🎨 Frontend Integration

### SkillGapAnalysis Component

The component now accepts `edgeCaseHandlerData` prop:

```typescript
interface SkillGapAnalysisProps {
  // ... other props
  edgeCaseHandlerData?: {
    sanitizedInput?: any;
    isNewUser?: boolean;
    onboardingRecommendations?: string[];
    retryCount?: number;
    fallbackDataUsed?: boolean;
    suggestedAlternatives?: string[];
  };
}
```

### Error Handling Display

```typescript
// Display EdgeCaseHandler information
{edgeCaseHandlerData?.fallbackDataUsed && (
  <Card className="border-yellow-200 bg-yellow-50">
    <CardContent className="pt-6">
      <div className="flex items-center gap-2 text-yellow-800">
        <AlertTriangle className="h-4 w-4" />
        <span className="font-medium">Using Fallback Data</span>
      </div>
      <p className="text-sm text-yellow-700 mt-1">
        Some services were unavailable. Results may be limited.
      </p>
    </CardContent>
  </Card>
)}
```

## 🔧 Error Types & Handling

### Error Type Classification

1. **VALIDATION_ERROR** (400)
   - Invalid input data
   - Missing required fields
   - Data format issues

2. **AI_SERVICE_ERROR** (503)
   - AI service unavailable
   - API rate limits exceeded
   - Service timeouts

3. **CIRCUIT_BREAKER_OPEN** (503)
   - Service temporarily disabled
   - Too many failures detected
   - Automatic recovery in progress

4. **DATABASE_ERROR** (500)
   - Database connection issues
   - Query failures
   - Data integrity problems

### Fallback Strategies

1. **AI Service Fallback**
   - Use cached results if available
   - Provide basic skill gap analysis
   - Suggest manual assessment

2. **Database Fallback**
   - Use in-memory data structures
   - Provide default career path data
   - Enable offline mode

3. **User Experience Fallback**
   - Show helpful error messages
   - Provide alternative actions
   - Maintain form state

## 🚨 Troubleshooting Guide

### Common Issues

#### 1. EdgeCaseHandler Not Responding
**Symptoms**: API calls timeout, no fallback data provided
**Solution**: 
```bash
# Check EdgeCaseHandler service status
curl -X GET /api/health/edge-case-handler

# Restart service if needed
pm2 restart edge-case-handler
```

#### 2. Fallback Data Not Displaying
**Symptoms**: Error responses don't include fallbackData
**Solution**: Verify EdgeCaseHandler integration in API routes:
```typescript
// Ensure proper error response format
return NextResponse.json({
  success: false,
  error: analysisResult.error,
  errorType: edgeCaseResult.errorType,
  fallbackData: edgeCaseResult.fallbackData, // ← Must be included
  suggestedAlternatives: edgeCaseResult.suggestedAlternatives
}, { status: 500 });
```

#### 3. Frontend Not Handling EdgeCase Data
**Symptoms**: Error information not displayed to users
**Solution**: Check component props:
```typescript
<SkillGapAnalysis
  // ... other props
  edgeCaseHandlerData={analysisResult.metadata?.edgeCaseHandlerData} // ← Required
/>
```

### Performance Issues

#### 1. Slow EdgeCaseHandler Response
**Monitoring**: Check performance metrics
```typescript
// Monitor EdgeCaseHandler performance
const metrics = await edgeCaseHandlerService.getPerformanceMetrics();
console.log('Average response time:', metrics.averageResponseTime);
```

#### 2. High Memory Usage
**Solution**: Implement cache cleanup
```typescript
// Clear EdgeCaseHandler cache
await edgeCaseHandlerService.clearCache();
```

## 📈 Monitoring & Metrics

### Key Metrics to Monitor

1. **Success Rate**: EdgeCaseHandler success vs fallback usage
2. **Response Time**: Average handling time for edge cases
3. **Error Distribution**: Types of errors being handled
4. **Cache Hit Rate**: Effectiveness of caching strategy

### Monitoring Setup

```typescript
// Example monitoring integration
import { PerformanceMonitor } from '@/lib/performance-monitor';

const monitor = new PerformanceMonitor();
monitor.trackEdgeCaseHandler({
  operation: 'skills-analysis',
  success: edgeCaseResult.success,
  responseTime: Date.now() - startTime,
  errorType: edgeCaseResult.errorType
});
```

## 🔄 Deployment Considerations

### Feature Flags

EdgeCaseHandler can be controlled via feature flags:

```typescript
// Disable EdgeCaseHandler if issues arise
const useEdgeCaseHandler = await featureFlags.isEnabled('edge-case-handler', userId);

if (useEdgeCaseHandler) {
  // Use EdgeCaseHandler
} else {
  // Use direct service calls
}
```

### Rollback Strategy

1. **Immediate Rollback**: Disable via feature flag
2. **Gradual Rollback**: Reduce traffic percentage
3. **Full Rollback**: Revert to previous API implementation

## ✅ Testing Integration

### Unit Tests
```bash
npm test -- --testPathPattern=edgeCaseHandler
```

### Integration Tests
```bash
npm run test:integration -- --grep "EdgeCaseHandler"
```

### End-to-End Tests
```bash
npm run test:e2e -- --spec="skill-gap-analyzer-edge-cases"
```

## 📚 Additional Resources

- [EdgeCaseHandler Service Documentation](../api/EDGE_CASE_HANDLER_SERVICE.md)
- [API Response Format Specification](../api/API_RESPONSE_FORMATS.md)
- [Performance Monitoring Guide](../monitoring/PERFORMANCE_MONITORING.md)
- [Error Handling Best Practices](../development/ERROR_HANDLING.md)
