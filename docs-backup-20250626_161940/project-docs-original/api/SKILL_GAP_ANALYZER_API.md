# Skill Gap Analyzer API Documentation

## Overview

The Skill Gap Analyzer API provides comprehensive endpoints for skill assessment, gap analysis, and personalized learning recommendations. This API enables users to evaluate their current skills, identify gaps relative to career goals, and receive AI-powered recommendations for skill development.

## Base URL

```
Production: https://faafo.com/api
Development: http://localhost:3000/api
```

## Authentication

All API endpoints require authentication via <PERSON><PERSON><PERSON> token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

## Rate Limiting

- **General endpoints**: 100 requests per minute per user
- **AI-powered endpoints**: 20 requests per minute per user
- **Search endpoints**: 200 requests per minute per user

## Error Handling

All endpoints return standardized error responses:

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {
    "field": "validation error details"
  },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "requestId": "req_123456789"
}
```

### Error Codes

- `VALIDATION_ERROR`: Input validation failed
- `AUTHENTICATION_ERROR`: Invalid or missing authentication
- `AUTHORIZATION_ERROR`: Insufficient permissions
- `RATE_LIMIT_ERROR`: Rate limit exceeded
- `AI_SERVICE_ERROR`: AI service unavailable
- `INTERNAL_ERROR`: Internal server error

## Endpoints

### 1. Skill Search

Search for skills in the database with autocomplete and filtering.

#### GET /skills/search

**Query Parameters:**
- `q` (string, required): Search query
- `limit` (number, optional): Maximum results (default: 10, max: 50)
- `category` (string, optional): Filter by skill category
- `level` (string, optional): Filter by skill level (BEGINNER, INTERMEDIATE, ADVANCED, EXPERT)

**Example Request:**
```bash
GET /api/skills/search?q=javascript&limit=5&category=programming
```

**Example Response:**
```json
{
  "skills": [
    {
      "id": "skill_123",
      "name": "JavaScript",
      "category": "Programming Languages",
      "description": "Dynamic programming language for web development",
      "marketDemand": "HIGH",
      "averageSalary": 85000,
      "relatedSkills": ["React", "Node.js", "TypeScript"]
    }
  ],
  "totalCount": 1,
  "searchTime": 45
}
```

### 2. Skill Assessment

Submit and manage skill assessments.

#### POST /skills/assessment

Submit a skill assessment for the authenticated user.

**Request Body:**
```json
{
  "assessments": [
    {
      "skillName": "JavaScript",
      "selfRating": 8,
      "confidenceLevel": 7,
      "yearsOfExperience": 3,
      "lastUsed": "2024-01-01",
      "notes": "Strong in ES6+ features"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "assessmentId": "assessment_123",
  "processedSkills": 1,
  "recommendations": [
    {
      "type": "SKILL_IMPROVEMENT",
      "message": "Consider learning TypeScript to enhance your JavaScript skills",
      "priority": "MEDIUM"
    }
  ]
}
```

#### GET /skills/assessment/user

Get user's skill assessments.

**Query Parameters:**
- `limit` (number, optional): Maximum results (default: 20)
- `offset` (number, optional): Pagination offset (default: 0)
- `skillCategory` (string, optional): Filter by category

**Response:**
```json
{
  "assessments": [
    {
      "id": "assessment_123",
      "skillName": "JavaScript",
      "selfRating": 8,
      "confidenceLevel": 7,
      "yearsOfExperience": 3,
      "lastUsed": "2024-01-01",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "totalCount": 1,
  "pagination": {
    "limit": 20,
    "offset": 0,
    "hasMore": false
  }
}
```

### 3. Skill Gap Analysis

Analyze skill gaps and generate recommendations.

#### POST /ai/skills-analysis/comprehensive

Perform comprehensive skill gap analysis using AI.

**Request Body:**
```json
{
  "currentSkills": [
    {
      "skillName": "JavaScript",
      "selfRating": 8,
      "confidenceLevel": 7,
      "yearsOfExperience": 3
    }
  ],
  "targetCareerPath": {
    "careerPathName": "Full Stack Developer",
    "targetLevel": "SENIOR"
  },
  "preferences": {
    "timeframe": "SIX_MONTHS",
    "hoursPerWeek": 10,
    "learningStyle": "MIXED",
    "budgetRange": "MODERATE",
    "focusAreas": ["Technical Skills", "Soft Skills"]
  },
  "includeMarketData": true,
  "includePersonalizedPaths": true
}
```

**Response:**
```json
{
  "analysisId": "analysis_123",
  "readinessScore": 75,
  "skillGaps": [
    {
      "skillName": "React",
      "currentLevel": 0,
      "requiredLevel": 8,
      "priority": "HIGH",
      "estimatedLearningTime": "2-3 months",
      "marketDemand": "VERY_HIGH"
    }
  ],
  "recommendations": [
    {
      "type": "LEARNING_PATH",
      "title": "React Fundamentals to Advanced",
      "description": "Complete React learning path",
      "estimatedDuration": "3 months",
      "resources": [
        {
          "title": "React Official Tutorial",
          "type": "TUTORIAL",
          "url": "https://react.dev/tutorial",
          "difficulty": "BEGINNER",
          "estimatedTime": "8 hours"
        }
      ]
    }
  ],
  "marketInsights": {
    "salaryIncrease": "15-25%",
    "jobOpportunities": 1250,
    "trendingSkills": ["React", "TypeScript", "Next.js"]
  },
  "personalizedPath": {
    "totalDuration": "6 months",
    "weeklyCommitment": "10 hours",
    "milestones": [
      {
        "week": 4,
        "goal": "Complete React basics",
        "skills": ["JSX", "Components", "Props"]
      }
    ]
  }
}
```

#### GET /skills/gap-analysis/user

Get user's previous gap analyses.

**Query Parameters:**
- `limit` (number, optional): Maximum results (default: 10)
- `includeDetails` (boolean, optional): Include full analysis details

**Response:**
```json
{
  "analyses": [
    {
      "id": "analysis_123",
      "careerPath": "Full Stack Developer",
      "readinessScore": 75,
      "skillGapsCount": 3,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "status": "COMPLETED"
    }
  ],
  "totalCount": 1
}
```

### 4. Learning Path Recommendations

Get personalized learning paths and track progress.

#### POST /ai/learning-paths/generate

Generate personalized learning paths.

**Request Body:**
```json
{
  "skillGaps": [
    {
      "skillName": "React",
      "currentLevel": 0,
      "targetLevel": 8,
      "priority": "HIGH"
    }
  ],
  "preferences": {
    "timeframe": "THREE_MONTHS",
    "hoursPerWeek": 15,
    "learningStyle": "HANDS_ON",
    "budgetRange": "LOW"
  },
  "careerGoals": {
    "targetRole": "Frontend Developer",
    "targetLevel": "MID_LEVEL"
  }
}
```

**Response:**
```json
{
  "learningPathId": "path_123",
  "title": "Frontend Developer Learning Path",
  "description": "Comprehensive path to become a frontend developer",
  "totalDuration": "3 months",
  "weeklyCommitment": "15 hours",
  "phases": [
    {
      "phaseNumber": 1,
      "title": "React Fundamentals",
      "duration": "4 weeks",
      "skills": ["JSX", "Components", "Props", "State"],
      "resources": [
        {
          "title": "React Basics Course",
          "type": "COURSE",
          "provider": "FreeCodeCamp",
          "url": "https://freecodecamp.org/react",
          "difficulty": "BEGINNER",
          "estimatedTime": "20 hours",
          "cost": "FREE"
        }
      ],
      "projects": [
        {
          "title": "Todo App",
          "description": "Build a simple todo application",
          "difficulty": "BEGINNER",
          "estimatedTime": "8 hours"
        }
      ]
    }
  ],
  "milestones": [
    {
      "week": 2,
      "title": "First React Component",
      "description": "Create your first functional component"
    }
  ]
}
```

### 5. Market Data

Access skill market data and trends.

#### GET /skills/market-data

Get market data for skills.

**Query Parameters:**
- `skills` (string[], required): Array of skill names
- `location` (string, optional): Geographic location (default: "global")
- `includeProjections` (boolean, optional): Include future projections

**Response:**
```json
{
  "marketData": [
    {
      "skillName": "React",
      "demand": "VERY_HIGH",
      "averageSalary": {
        "junior": 65000,
        "mid": 85000,
        "senior": 120000
      },
      "jobPostings": 15420,
      "growthRate": "12%",
      "topCompanies": ["Meta", "Netflix", "Airbnb"],
      "relatedSkills": ["JavaScript", "TypeScript", "Redux"],
      "projections": {
        "nextYear": {
          "demandChange": "+8%",
          "salaryChange": "+5%"
        }
      }
    }
  ],
  "lastUpdated": "2024-01-01T00:00:00.000Z"
}
```

### 6. Progress Tracking

Track learning progress and achievements.

#### POST /skills/progress

Update skill learning progress.

**Request Body:**
```json
{
  "skillName": "React",
  "progressType": "COURSE_COMPLETED",
  "progressValue": 100,
  "resourceId": "course_123",
  "timeSpent": 120,
  "notes": "Completed React fundamentals course"
}
```

**Response:**
```json
{
  "success": true,
  "progressId": "progress_123",
  "currentLevel": 6,
  "previousLevel": 4,
  "achievements": [
    {
      "type": "SKILL_MILESTONE",
      "title": "React Intermediate",
      "description": "Reached intermediate level in React",
      "earnedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

#### GET /skills/progress/user

Get user's learning progress.

**Query Parameters:**
- `skillName` (string, optional): Filter by specific skill
- `timeframe` (string, optional): Filter by timeframe (WEEK, MONTH, YEAR)

**Response:**
```json
{
  "progress": [
    {
      "skillName": "React",
      "currentLevel": 6,
      "targetLevel": 8,
      "progressPercentage": 75,
      "timeSpent": 240,
      "lastActivity": "2024-01-01T00:00:00.000Z",
      "streak": 7,
      "achievements": 3
    }
  ],
  "summary": {
    "totalSkills": 5,
    "averageProgress": 68,
    "totalTimeSpent": 1200,
    "achievementsEarned": 12
  }
}
```

## Webhooks

The API supports webhooks for real-time notifications.

### Webhook Events

- `skill.assessment.completed`: User completed skill assessment
- `analysis.completed`: Skill gap analysis completed
- `progress.milestone`: User reached learning milestone
- `recommendation.generated`: New recommendations available

### Webhook Payload

```json
{
  "event": "skill.assessment.completed",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "userId": "user_123",
  "data": {
    "assessmentId": "assessment_123",
    "skillsAssessed": 5,
    "averageRating": 7.2
  }
}
```

## SDK Examples

### JavaScript/TypeScript

```typescript
import { SkillGapAnalyzerAPI } from '@faafo/skill-gap-sdk';

const api = new SkillGapAnalyzerAPI({
  baseURL: 'https://faafo.com/api',
  apiKey: 'your-api-key'
});

// Search skills
const skills = await api.skills.search('javascript');

// Submit assessment
const assessment = await api.skills.submitAssessment([
  {
    skillName: 'JavaScript',
    selfRating: 8,
    confidenceLevel: 7,
    yearsOfExperience: 3
  }
]);

// Analyze skill gaps
const analysis = await api.analysis.comprehensive({
  currentSkills: assessment.skills,
  targetCareerPath: {
    careerPathName: 'Full Stack Developer',
    targetLevel: 'SENIOR'
  }
});
```

### Python

```python
from faafo_skill_gap import SkillGapAnalyzerAPI

api = SkillGapAnalyzerAPI(
    base_url='https://faafo.com/api',
    api_key='your-api-key'
)

# Search skills
skills = api.skills.search('python')

# Submit assessment
assessment = api.skills.submit_assessment([
    {
        'skillName': 'Python',
        'selfRating': 9,
        'confidenceLevel': 8,
        'yearsOfExperience': 5
    }
])
```

## Testing

### Test Environment

```
Base URL: https://api-test.faafo.com/api
Test API Key: test_sk_1234567890abcdef
```

### Sample Test Data

The test environment includes sample data for:
- 500+ skills across various categories
- 50+ career paths
- Market data for popular skills
- Sample user assessments and analyses

## Support

- **Documentation**: https://docs.faafo.com/skill-gap-analyzer
- **API Status**: https://status.faafo.com
- **Support Email**: <EMAIL>
- **Developer Discord**: https://discord.gg/faafo-dev
