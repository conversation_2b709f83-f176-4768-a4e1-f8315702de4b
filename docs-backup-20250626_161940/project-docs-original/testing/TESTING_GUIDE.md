# Skill Gap Analyzer - Testing Guide

## Table of Contents

1. [Testing Strategy](#testing-strategy)
2. [Test Types & Coverage](#test-types--coverage)
3. [Unit Testing](#unit-testing)
4. [Integration Testing](#integration-testing)
5. [End-to-End Testing](#end-to-end-testing)
6. [Performance Testing](#performance-testing)
7. [Security Testing](#security-testing)
8. [Test Data Management](#test-data-management)
9. [CI/CD Integration](#cicd-integration)
10. [Best Practices](#best-practices)

## Testing Strategy

### Testing Pyramid

```
    /\
   /  \     E2E Tests (10%)
  /____\    - Critical user journeys
 /      \   - Cross-browser compatibility
/__________\ Integration Tests (20%)
            - API endpoints
            - Database operations
            - External service integration
            
            Unit Tests (70%)
            - Business logic
            - Utility functions
            - Component behavior
```

### Test Coverage Goals

- **Overall Coverage**: 90%+
- **Critical Paths**: 100%
- **Business Logic**: 95%+
- **API Endpoints**: 100%
- **UI Components**: 85%+

### Testing Environments

1. **Local Development**: Jest + React Testing Library
2. **CI/CD Pipeline**: Automated test suite
3. **Staging**: Full integration and E2E tests
4. **Production**: Smoke tests and monitoring

## Test Types & Coverage

### Current Test Coverage

```bash
# Run coverage report
npm run test:coverage

# Coverage by category
- Unit Tests: 156 tests
- Integration Tests: 45 tests
- E2E Tests: 25 tests
- Performance Tests: 15 tests
- Security Tests: 20 tests
```

### Test Categories

1. **Functional Tests**
   - Skill search and filtering
   - Assessment submission and validation
   - AI analysis and recommendations
   - Progress tracking and analytics

2. **Non-Functional Tests**
   - Performance and load testing
   - Security and vulnerability testing
   - Accessibility and usability testing
   - Cross-browser compatibility

3. **Integration Tests**
   - Database operations
   - AI service integration
   - Email service integration
   - Cache layer functionality

## Unit Testing

### Framework: Jest + React Testing Library

**Configuration** (`jest.config.js`):
```javascript
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
  ],
  coverageThreshold: {
    global: {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90,
    },
  },
};
```

### Component Testing Examples

**Skill Assessment Form:**
```typescript
// __tests__/components/SkillAssessmentForm.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SkillAssessmentForm } from '@/components/SkillAssessmentForm';

describe('SkillAssessmentForm', () => {
  const mockOnSubmit = jest.fn();
  
  beforeEach(() => {
    mockOnSubmit.mockClear();
  });

  it('should render skill assessment form', () => {
    render(<SkillAssessmentForm onSubmit={mockOnSubmit} />);
    
    expect(screen.getByText('Skill Assessment')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /submit/i })).toBeInTheDocument();
  });

  it('should validate required fields', async () => {
    render(<SkillAssessmentForm onSubmit={mockOnSubmit} />);
    
    fireEvent.click(screen.getByRole('button', { name: /submit/i }));
    
    await waitFor(() => {
      expect(screen.getByText('Skill name is required')).toBeInTheDocument();
    });
  });

  it('should submit valid assessment', async () => {
    render(<SkillAssessmentForm onSubmit={mockOnSubmit} />);
    
    // Fill form
    fireEvent.change(screen.getByLabelText(/skill name/i), {
      target: { value: 'JavaScript' }
    });
    fireEvent.change(screen.getByLabelText(/self rating/i), {
      target: { value: '8' }
    });
    
    fireEvent.click(screen.getByRole('button', { name: /submit/i }));
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith([
        expect.objectContaining({
          skillName: 'JavaScript',
          selfRating: 8,
        })
      ]);
    });
  });
});
```

**Business Logic Testing:**
```typescript
// __tests__/lib/skills/skill-analysis.test.ts
import { calculateReadinessScore, identifySkillGaps } from '@/lib/skills/skill-analysis';

describe('Skill Analysis', () => {
  describe('calculateReadinessScore', () => {
    it('should calculate correct readiness score', () => {
      const currentSkills = [
        { skillName: 'JavaScript', selfRating: 8, requiredLevel: 7 },
        { skillName: 'React', selfRating: 6, requiredLevel: 8 },
        { skillName: 'Node.js', selfRating: 5, requiredLevel: 6 },
      ];
      
      const score = calculateReadinessScore(currentSkills);
      expect(score).toBe(75); // Expected calculation result
    });

    it('should handle empty skills array', () => {
      const score = calculateReadinessScore([]);
      expect(score).toBe(0);
    });
  });

  describe('identifySkillGaps', () => {
    it('should identify critical skill gaps', () => {
      const currentSkills = [
        { skillName: 'JavaScript', selfRating: 8 },
      ];
      const requiredSkills = [
        { skillName: 'JavaScript', requiredLevel: 7 },
        { skillName: 'React', requiredLevel: 8 },
        { skillName: 'TypeScript', requiredLevel: 6 },
      ];
      
      const gaps = identifySkillGaps(currentSkills, requiredSkills);
      
      expect(gaps).toHaveLength(2);
      expect(gaps[0]).toMatchObject({
        skillName: 'React',
        priority: 'CRITICAL',
        currentLevel: 0,
        requiredLevel: 8,
      });
    });
  });
});
```

### Service Layer Testing

```typescript
// __tests__/lib/services/skill-service.test.ts
import { SkillService } from '@/lib/services/skill-service';
import { prismaMock } from '../__mocks__/prisma';

jest.mock('@/lib/prisma', () => ({
  prisma: prismaMock,
}));

describe('SkillService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('searchSkills', () => {
    it('should return skills matching query', async () => {
      const mockSkills = [
        { id: '1', name: 'JavaScript', category: 'Programming' },
        { id: '2', name: 'Java', category: 'Programming' },
      ];
      
      prismaMock.skill.findMany.mockResolvedValue(mockSkills);
      
      const result = await SkillService.searchSkills('java');
      
      expect(result.skills).toHaveLength(2);
      expect(prismaMock.skill.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            name: expect.objectContaining({
              contains: 'java',
              mode: 'insensitive',
            }),
          }),
        })
      );
    });
  });
});
```

## Integration Testing

### API Endpoint Testing

```typescript
// __tests__/api/skills/search.test.ts
import { createMocks } from 'node-mocks-http';
import handler from '@/pages/api/skills/search';

describe('/api/skills/search', () => {
  it('should return skills for valid query', async () => {
    const { req, res } = createMocks({
      method: 'GET',
      query: { q: 'javascript', limit: '5' },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    
    const data = JSON.parse(res._getData());
    expect(data).toHaveProperty('skills');
    expect(data.skills).toBeInstanceOf(Array);
    expect(data.skills.length).toBeLessThanOrEqual(5);
  });

  it('should return 400 for missing query', async () => {
    const { req, res } = createMocks({
      method: 'GET',
      query: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    
    const data = JSON.parse(res._getData());
    expect(data).toHaveProperty('error');
  });

  it('should handle rate limiting', async () => {
    // Simulate multiple rapid requests
    const requests = Array.from({ length: 101 }, () => 
      createMocks({
        method: 'GET',
        query: { q: 'test' },
        headers: { 'x-forwarded-for': '127.0.0.1' },
      })
    );

    const responses = await Promise.all(
      requests.map(({ req, res }) => handler(req, res))
    );

    // Should have some rate-limited responses
    const rateLimited = responses.filter(res => res._getStatusCode() === 429);
    expect(rateLimited.length).toBeGreaterThan(0);
  });
});
```

### Database Integration Testing

```typescript
// __tests__/integration/database.test.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.TEST_DATABASE_URL,
    },
  },
});

describe('Database Integration', () => {
  beforeAll(async () => {
    await prisma.$connect();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Clean database before each test
    await prisma.userSkillAssessment.deleteMany();
    await prisma.skill.deleteMany();
  });

  it('should create and retrieve skill assessment', async () => {
    // Create test skill
    const skill = await prisma.skill.create({
      data: {
        name: 'JavaScript',
        category: 'Programming',
        description: 'Programming language',
      },
    });

    // Create test user assessment
    const assessment = await prisma.userSkillAssessment.create({
      data: {
        userId: 'test-user-id',
        skillName: skill.name,
        selfRating: 8,
        confidenceLevel: 7,
        yearsOfExperience: 3,
      },
    });

    // Retrieve and verify
    const retrieved = await prisma.userSkillAssessment.findUnique({
      where: { id: assessment.id },
    });

    expect(retrieved).toMatchObject({
      skillName: 'JavaScript',
      selfRating: 8,
      confidenceLevel: 7,
    });
  });
});
```

## End-to-End Testing

### Framework: Playwright

**Configuration** (`playwright.config.ts`):
```typescript
import { defineConfig } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: !process.env.CI,
  },
});
```

### E2E Test Examples

**Complete Skill Assessment Flow:**
```typescript
// e2e/skill-assessment.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Skill Assessment Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Login as test user
    await page.goto('/login');
    await page.fill('[data-testid=email]', '<EMAIL>');
    await page.fill('[data-testid=password]', 'testpassword');
    await page.click('[data-testid=login-button]');
    
    // Navigate to skill assessment
    await page.goto('/skills/assessment');
  });

  test('should complete skill assessment successfully', async ({ page }) => {
    // Add first skill
    await page.fill('[data-testid=skill-search]', 'JavaScript');
    await page.click('[data-testid=skill-suggestion]:first-child');
    
    // Rate the skill
    await page.click('[data-testid=rating-8]');
    await page.click('[data-testid=confidence-7]');
    await page.fill('[data-testid=years-experience]', '3');
    
    // Add skill to assessment
    await page.click('[data-testid=add-skill]');
    
    // Verify skill was added
    await expect(page.locator('[data-testid=skill-item]')).toContainText('JavaScript');
    
    // Submit assessment
    await page.click('[data-testid=submit-assessment]');
    
    // Verify success
    await expect(page.locator('[data-testid=success-message]')).toBeVisible();
    await expect(page).toHaveURL(/.*\/skills\/assessment\/success/);
  });

  test('should validate required fields', async ({ page }) => {
    // Try to submit without skills
    await page.click('[data-testid=submit-assessment]');
    
    // Should show validation error
    await expect(page.locator('[data-testid=error-message]')).toContainText(
      'At least one skill assessment is required'
    );
  });

  test('should save draft automatically', async ({ page }) => {
    // Add skill but don't submit
    await page.fill('[data-testid=skill-search]', 'React');
    await page.click('[data-testid=skill-suggestion]:first-child');
    await page.click('[data-testid=rating-6]');
    await page.click('[data-testid=add-skill]');
    
    // Refresh page
    await page.reload();
    
    // Should restore draft
    await expect(page.locator('[data-testid=skill-item]')).toContainText('React');
  });
});
```

**AI Analysis Flow:**
```typescript
// e2e/ai-analysis.spec.ts
import { test, expect } from '@playwright/test';

test.describe('AI Analysis Flow', () => {
  test('should generate skill gap analysis', async ({ page }) => {
    // Complete prerequisite assessment
    await page.goto('/skills/assessment');
    // ... assessment steps ...
    
    // Navigate to analysis
    await page.goto('/skills/analysis');
    
    // Select career path
    await page.selectOption('[data-testid=career-path]', 'Full Stack Developer');
    await page.selectOption('[data-testid=target-level]', 'SENIOR');
    
    // Set preferences
    await page.selectOption('[data-testid=timeframe]', 'SIX_MONTHS');
    await page.fill('[data-testid=hours-per-week]', '10');
    
    // Start analysis
    await page.click('[data-testid=start-analysis]');
    
    // Wait for analysis to complete (with timeout)
    await expect(page.locator('[data-testid=analysis-results]')).toBeVisible({
      timeout: 30000
    });
    
    // Verify results
    await expect(page.locator('[data-testid=readiness-score]')).toBeVisible();
    await expect(page.locator('[data-testid=skill-gaps]')).toBeVisible();
    await expect(page.locator('[data-testid=recommendations]')).toBeVisible();
  });
});
```

## Performance Testing

### Load Testing with Custom Scripts

```typescript
// scripts/performance/load-test-skill-gap.js
const SkillGapLoadTester = require('./load-test-skill-gap');

const tester = new SkillGapLoadTester({
  baseUrl: 'http://localhost:3000',
  concurrentUsers: 10,
  testDuration: 60000,
});

// Run performance tests
npm run test:performance
```

### Performance Benchmarks

```typescript
// __tests__/performance/benchmarks.test.ts
describe('Performance Benchmarks', () => {
  it('should meet skill search performance requirements', async () => {
    const startTime = Date.now();
    
    const response = await fetch('/api/skills/search?q=javascript');
    const data = await response.json();
    
    const responseTime = Date.now() - startTime;
    
    expect(responseTime).toBeLessThan(500); // 500ms requirement
    expect(data.skills.length).toBeGreaterThan(0);
  });

  it('should handle concurrent requests efficiently', async () => {
    const requests = Array.from({ length: 50 }, () =>
      fetch('/api/skills/search?q=react')
    );
    
    const startTime = Date.now();
    const responses = await Promise.all(requests);
    const totalTime = Date.now() - startTime;
    
    // All requests should complete within 5 seconds
    expect(totalTime).toBeLessThan(5000);
    
    // All requests should succeed
    responses.forEach(response => {
      expect(response.ok).toBe(true);
    });
  });
});
```

## Security Testing

### Security Test Suite

```typescript
// __tests__/security/security.test.ts
describe('Security Tests', () => {
  it('should prevent SQL injection in skill search', async () => {
    const maliciousQuery = "'; DROP TABLE skills; --";
    
    const response = await fetch(
      `/api/skills/search?q=${encodeURIComponent(maliciousQuery)}`
    );
    
    expect(response.ok).toBe(true);
    // Should not crash or return error
  });

  it('should sanitize XSS attempts', async () => {
    const xssPayload = '<script>alert("xss")</script>';
    
    const response = await fetch('/api/skills/assessment', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        assessments: [{
          skillName: xssPayload,
          selfRating: 5,
        }]
      }),
    });
    
    const data = await response.json();
    
    // Should sanitize the input
    expect(JSON.stringify(data)).not.toContain('<script>');
  });
});
```

## Test Data Management

### Test Database Setup

```typescript
// scripts/test-db-setup.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient({
  datasources: {
    db: { url: process.env.TEST_DATABASE_URL },
  },
});

export async function setupTestData() {
  // Create test skills
  await prisma.skill.createMany({
    data: [
      {
        name: 'JavaScript',
        category: 'Programming',
        description: 'Dynamic programming language',
        marketDemand: 'HIGH',
      },
      {
        name: 'React',
        category: 'Frontend',
        description: 'JavaScript library for building UIs',
        marketDemand: 'VERY_HIGH',
      },
    ],
  });
}

export async function cleanupTestData() {
  await prisma.userSkillAssessment.deleteMany();
  await prisma.skill.deleteMany();
}
```

### Test Fixtures

```typescript
// __tests__/__fixtures__/skills.ts
export const mockSkills = [
  {
    id: 'skill-1',
    name: 'JavaScript',
    category: 'Programming',
    description: 'Dynamic programming language',
    marketDemand: 'HIGH',
    averageSalary: 85000,
  },
  {
    id: 'skill-2',
    name: 'React',
    category: 'Frontend',
    description: 'JavaScript library',
    marketDemand: 'VERY_HIGH',
    averageSalary: 90000,
  },
];

export const mockAssessments = [
  {
    skillName: 'JavaScript',
    selfRating: 8,
    confidenceLevel: 7,
    yearsOfExperience: 3,
  },
];
```

## CI/CD Integration

### GitHub Actions Workflow

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Setup test database
        run: |
          npx prisma migrate deploy
          npm run db:seed:test
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
      
      - name: Run unit tests
        run: npm run test:unit
      
      - name: Run integration tests
        run: npm run test:integration
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Run performance tests
        run: npm run test:performance
      
      - name: Run security tests
        run: npm run test:security
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
```

## Best Practices

### Test Organization

1. **File Structure**
   ```
   __tests__/
   ├── unit/
   │   ├── components/
   │   ├── lib/
   │   └── utils/
   ├── integration/
   │   ├── api/
   │   └── database/
   ├── e2e/
   │   ├── flows/
   │   └── pages/
   ├── performance/
   ├── security/
   └── __fixtures__/
   ```

2. **Naming Conventions**
   - Test files: `*.test.ts` or `*.spec.ts`
   - E2E tests: `*.e2e.ts`
   - Fixtures: `*.fixture.ts`

3. **Test Data**
   - Use factories for test data generation
   - Clean up after each test
   - Use realistic but anonymized data

### Writing Effective Tests

1. **AAA Pattern**: Arrange, Act, Assert
2. **Single Responsibility**: One assertion per test
3. **Descriptive Names**: Clear test descriptions
4. **Independent Tests**: No test dependencies
5. **Fast Execution**: Optimize for speed

### Continuous Improvement

1. **Monitor Test Performance**: Track test execution times
2. **Review Test Coverage**: Regular coverage analysis
3. **Update Test Data**: Keep test scenarios current
4. **Refactor Tests**: Maintain test code quality
5. **Team Training**: Regular testing best practices sessions

---

*Last Updated: January 2024*
*Version: 2.0*
