# Skill Gap Analyzer - Technical Documentation

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [System Components](#system-components)
3. [Database Schema](#database-schema)
4. [API Implementation](#api-implementation)
5. [AI Integration](#ai-integration)
6. [Frontend Components](#frontend-components)
7. [Security Implementation](#security-implementation)
8. [Performance Optimization](#performance-optimization)
9. [Monitoring & Logging](#monitoring--logging)
10. [Deployment & DevOps](#deployment--devops)

## Architecture Overview

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   AI Services   │
│   (Next.js)     │◄──►│   (Next.js)     │◄──►│   (Gemini)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   State Mgmt    │    │   Database      │    │   Cache Layer   │
│   (Zustand)     │    │   (PostgreSQL)  │    │   (Redis)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Technology Stack

**Frontend:**
- Next.js 14 with App Router
- TypeScript for type safety
- Tailwind CSS for styling
- <PERSON>ust<PERSON> for state management
- React Hook Form for form handling
- Zod for validation

**Backend:**
- Next.js API Routes
- Prisma ORM for database operations
- PostgreSQL for data persistence
- Redis for caching and sessions
- <PERSON> for logging

**AI & External Services:**
- Google Gemini for AI analysis
- Resend for email notifications
- Sentry for error monitoring

**Infrastructure:**
- Vercel for hosting and deployment
- Neon for PostgreSQL hosting
- Upstash for Redis hosting

## System Components

### Core Modules

#### 1. Skill Management (`/src/lib/skills/`)

**Purpose**: Handle skill data, search, and categorization

**Key Files:**
- `skill-service.ts` - Core skill operations
- `skill-search.ts` - Search functionality with caching
- `skill-categories.ts` - Skill categorization logic
- `market-data.ts` - Market insights and salary data

**Key Functions:**
```typescript
// Search skills with caching
async function searchSkills(query: string, options: SearchOptions): Promise<SkillSearchResult>

// Get skill details with market data
async function getSkillDetails(skillId: string): Promise<SkillDetails>

// Update skill market data
async function updateMarketData(skillId: string, data: MarketData): Promise<void>
```

#### 2. Assessment Engine (`/src/lib/assessment/`)

**Purpose**: Manage skill assessments and user evaluations

**Key Files:**
- `assessment-service.ts` - Assessment CRUD operations
- `assessment-validation.ts` - Input validation and sanitization
- `assessment-analytics.ts` - Assessment data analysis

**Key Functions:**
```typescript
// Submit skill assessment
async function submitAssessment(userId: string, assessments: SkillAssessment[]): Promise<AssessmentResult>

// Get user assessments with filtering
async function getUserAssessments(userId: string, filters: AssessmentFilters): Promise<UserAssessment[]>

// Calculate assessment statistics
async function calculateAssessmentStats(userId: string): Promise<AssessmentStats>
```

#### 3. AI Analysis Engine (`/src/lib/ai/`)

**Purpose**: AI-powered skill gap analysis and recommendations

**Key Files:**
- `skills-analysis.ts` - Main analysis orchestrator
- `gemini-service.ts` - Google Gemini integration
- `prompt-templates.ts` - AI prompt management
- `analysis-cache.ts` - Analysis result caching

**Key Functions:**
```typescript
// Comprehensive skill gap analysis
async function analyzeSkillGaps(request: AnalysisRequest): Promise<AnalysisResult>

// Generate learning recommendations
async function generateRecommendations(gaps: SkillGap[], preferences: UserPreferences): Promise<Recommendation[]>

// Get market insights for skills
async function getMarketInsights(skills: string[]): Promise<MarketInsights>
```

#### 4. Progress Tracking (`/src/lib/progress/`)

**Purpose**: Track learning progress and achievements

**Key Files:**
- `progress-service.ts` - Progress tracking operations
- `milestone-engine.ts` - Achievement and milestone logic
- `learning-analytics.ts` - Progress analytics

### Database Schema

#### Core Tables

**Skills Table:**
```sql
CREATE TABLE skills (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL UNIQUE,
  category VARCHAR(100) NOT NULL,
  description TEXT,
  market_demand VARCHAR(20) DEFAULT 'MEDIUM',
  average_salary INTEGER,
  growth_rate DECIMAL(5,2),
  related_skills TEXT[], -- Array of related skill names
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_skills_name ON skills(name);
CREATE INDEX idx_skills_category ON skills(category);
CREATE INDEX idx_skills_market_demand ON skills(market_demand);
```

**User Assessments Table:**
```sql
CREATE TABLE user_skill_assessments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  skill_name VARCHAR(255) NOT NULL,
  self_rating INTEGER CHECK (self_rating >= 1 AND self_rating <= 10),
  confidence_level INTEGER CHECK (confidence_level >= 1 AND confidence_level <= 10),
  years_of_experience DECIMAL(3,1) DEFAULT 0,
  last_used DATE,
  notes TEXT,
  assessment_type VARCHAR(20) DEFAULT 'SELF_ASSESSMENT',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(user_id, skill_name)
);

CREATE INDEX idx_assessments_user_id ON user_skill_assessments(user_id);
CREATE INDEX idx_assessments_skill_name ON user_skill_assessments(skill_name);
```

**Gap Analyses Table:**
```sql
CREATE TABLE skill_gap_analyses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  career_path VARCHAR(255) NOT NULL,
  target_level VARCHAR(50) NOT NULL,
  readiness_score INTEGER CHECK (readiness_score >= 0 AND readiness_score <= 100),
  analysis_data JSONB NOT NULL, -- Stores complete analysis results
  preferences JSONB, -- User preferences for the analysis
  status VARCHAR(20) DEFAULT 'COMPLETED',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_analyses_user_id ON skill_gap_analyses(user_id);
CREATE INDEX idx_analyses_career_path ON skill_gap_analyses(career_path);
CREATE INDEX idx_analyses_created_at ON skill_gap_analyses(created_at);
```

**Learning Progress Table:**
```sql
CREATE TABLE learning_progress (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  skill_name VARCHAR(255) NOT NULL,
  current_level INTEGER CHECK (current_level >= 1 AND current_level <= 10),
  target_level INTEGER CHECK (target_level >= 1 AND target_level <= 10),
  time_spent INTEGER DEFAULT 0, -- Minutes spent learning
  last_activity TIMESTAMP DEFAULT NOW(),
  progress_data JSONB, -- Detailed progress information
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(user_id, skill_name)
);

CREATE INDEX idx_progress_user_id ON learning_progress(user_id);
CREATE INDEX idx_progress_skill_name ON learning_progress(skill_name);
```

### API Implementation

#### Request/Response Patterns

**Standard Response Format:**
```typescript
interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, any>;
  };
  meta?: {
    timestamp: string;
    requestId: string;
    version: string;
  };
}
```

**Error Handling Middleware:**
```typescript
export function withErrorHandling<T>(
  handler: (req: NextRequest) => Promise<NextResponse<T>>
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    try {
      return await handler(req);
    } catch (error) {
      return handleAPIError(error, req);
    }
  };
}
```

**Rate Limiting Implementation:**
```typescript
const rateLimiter = new Map<string, { count: number; resetTime: number }>();

export function withRateLimit(limit: number, windowMs: number) {
  return function(handler: Function) {
    return async (req: NextRequest) => {
      const key = getClientKey(req);
      const now = Date.now();
      const windowStart = now - windowMs;
      
      // Clean old entries
      for (const [k, v] of rateLimiter.entries()) {
        if (v.resetTime < windowStart) {
          rateLimiter.delete(k);
        }
      }
      
      const current = rateLimiter.get(key) || { count: 0, resetTime: now + windowMs };
      
      if (current.count >= limit) {
        return NextResponse.json(
          { error: 'Rate limit exceeded' },
          { status: 429 }
        );
      }
      
      current.count++;
      rateLimiter.set(key, current);
      
      return handler(req);
    };
  };
}
```

### AI Integration

#### Gemini Service Implementation

**Service Configuration:**
```typescript
class GeminiService {
  private client: GoogleGenerativeAI;
  private model: GenerativeModel;
  
  constructor() {
    this.client = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
    this.model = this.client.getGenerativeModel({ 
      model: "gemini-1.5-pro",
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 8192,
      },
    });
  }
  
  async analyzeSkillGaps(request: AnalysisRequest): Promise<AnalysisResult> {
    const prompt = this.buildAnalysisPrompt(request);
    const result = await this.model.generateContent(prompt);
    return this.parseAnalysisResult(result.response.text());
  }
}
```

**Prompt Engineering:**
```typescript
function buildAnalysisPrompt(request: AnalysisRequest): string {
  return `
    You are an expert career advisor and skill assessment specialist. 
    Analyze the following skill assessment data and provide comprehensive recommendations.
    
    Current Skills:
    ${JSON.stringify(request.currentSkills, null, 2)}
    
    Target Career Path: ${request.targetCareerPath.careerPathName}
    Target Level: ${request.targetCareerPath.targetLevel}
    
    User Preferences:
    - Timeframe: ${request.preferences.timeframe}
    - Hours per week: ${request.preferences.hoursPerWeek}
    - Learning style: ${request.preferences.learningStyle}
    - Budget: ${request.preferences.budgetRange}
    
    Please provide:
    1. Skill gap analysis with priority levels
    2. Readiness score (0-100)
    3. Specific learning recommendations
    4. Estimated timeline for each skill
    5. Market insights and salary impact
    
    Format your response as valid JSON matching the AnalysisResult interface.
  `;
}
```

**Response Caching:**
```typescript
class AnalysisCache {
  private redis: Redis;
  
  async getCachedAnalysis(key: string): Promise<AnalysisResult | null> {
    const cached = await this.redis.get(`analysis:${key}`);
    return cached ? JSON.parse(cached) : null;
  }
  
  async cacheAnalysis(key: string, result: AnalysisResult, ttl: number = 3600): Promise<void> {
    await this.redis.setex(`analysis:${key}`, ttl, JSON.stringify(result));
  }
  
  generateCacheKey(request: AnalysisRequest): string {
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify({
      skills: request.currentSkills.map(s => ({ name: s.skillName, rating: s.selfRating })),
      careerPath: request.targetCareerPath,
      preferences: request.preferences
    }));
    return hash.digest('hex');
  }
}
```

### Frontend Components

#### Component Architecture

**Skill Assessment Component:**
```typescript
interface SkillAssessmentProps {
  initialSkills?: SkillAssessment[];
  onSubmit: (assessments: SkillAssessment[]) => Promise<void>;
  onSave?: (assessments: SkillAssessment[]) => void;
}

export function SkillAssessmentForm({ initialSkills, onSubmit, onSave }: SkillAssessmentProps) {
  const [assessments, setAssessments] = useState<SkillAssessment[]>(initialSkills || []);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Auto-save functionality
  useEffect(() => {
    const timer = setTimeout(() => {
      onSave?.(assessments);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [assessments, onSave]);
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Component implementation */}
    </form>
  );
}
```

**State Management with Zustand:**
```typescript
interface SkillGapStore {
  // State
  assessments: SkillAssessment[];
  analyses: SkillGapAnalysis[];
  currentAnalysis: SkillGapAnalysis | null;
  isLoading: boolean;
  
  // Actions
  setAssessments: (assessments: SkillAssessment[]) => void;
  addAssessment: (assessment: SkillAssessment) => void;
  updateAssessment: (id: string, updates: Partial<SkillAssessment>) => void;
  removeAssessment: (id: string) => void;
  
  // Async actions
  submitAssessments: () => Promise<void>;
  runAnalysis: (request: AnalysisRequest) => Promise<void>;
  loadUserData: () => Promise<void>;
}

export const useSkillGapStore = create<SkillGapStore>((set, get) => ({
  // Implementation
}));
```

### Security Implementation

#### Input Validation

**Zod Schemas:**
```typescript
export const SkillAssessmentSchema = z.object({
  skillName: z.string().min(1).max(255).trim(),
  selfRating: z.number().int().min(1).max(10),
  confidenceLevel: z.number().int().min(1).max(10),
  yearsOfExperience: z.number().min(0).max(50),
  lastUsed: z.string().optional(),
  notes: z.string().max(1000).optional(),
});

export const AnalysisRequestSchema = z.object({
  currentSkills: z.array(SkillAssessmentSchema).min(1).max(50),
  targetCareerPath: z.object({
    careerPathName: z.string().min(1).max(255),
    targetLevel: z.enum(['ENTRY', 'JUNIOR', 'MID', 'SENIOR', 'LEAD', 'PRINCIPAL']),
  }),
  preferences: z.object({
    timeframe: z.enum(['ONE_MONTH', 'THREE_MONTHS', 'SIX_MONTHS', 'ONE_YEAR']),
    hoursPerWeek: z.number().int().min(1).max(80),
    learningStyle: z.enum(['VISUAL', 'AUDITORY', 'KINESTHETIC', 'MIXED']),
    budgetRange: z.enum(['FREE', 'LOW', 'MODERATE', 'HIGH']),
    focusAreas: z.array(z.string()).optional(),
  }),
});
```

#### Authentication & Authorization

**JWT Middleware:**
```typescript
export async function withAuth(
  handler: (req: NextRequest, user: User) => Promise<NextResponse>
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    const token = extractToken(req);
    
    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    try {
      const user = await verifyToken(token);
      return handler(req, user);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }
  };
}
```

#### Data Sanitization

**Input Sanitization:**
```typescript
export function sanitizeInput(input: any): any {
  if (typeof input === 'string') {
    return input
      .trim()
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }
  
  if (Array.isArray(input)) {
    return input.map(sanitizeInput);
  }
  
  if (typeof input === 'object' && input !== null) {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(input)) {
      sanitized[key] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return input;
}
```

### Performance Optimization

#### Caching Strategy

**Multi-Level Caching:**
```typescript
class CacheManager {
  private memoryCache = new Map<string, { data: any; expires: number }>();
  private redis: Redis;
  
  async get<T>(key: string): Promise<T | null> {
    // Level 1: Memory cache
    const memCached = this.memoryCache.get(key);
    if (memCached && memCached.expires > Date.now()) {
      return memCached.data;
    }
    
    // Level 2: Redis cache
    const redisCached = await this.redis.get(key);
    if (redisCached) {
      const data = JSON.parse(redisCached);
      // Populate memory cache
      this.memoryCache.set(key, {
        data,
        expires: Date.now() + 300000 // 5 minutes
      });
      return data;
    }
    
    return null;
  }
  
  async set<T>(key: string, data: T, ttl: number = 3600): Promise<void> {
    // Set in both caches
    this.memoryCache.set(key, {
      data,
      expires: Date.now() + Math.min(ttl * 1000, 300000)
    });
    
    await this.redis.setex(key, ttl, JSON.stringify(data));
  }
}
```

#### Database Optimization

**Query Optimization:**
```typescript
// Efficient skill search with full-text search
export async function searchSkillsOptimized(
  query: string,
  options: SearchOptions
): Promise<SkillSearchResult> {
  const searchVector = `to_tsvector('english', name || ' ' || description)`;
  const searchQuery = `to_tsquery('english', $1)`;
  
  const skills = await prisma.$queryRaw`
    SELECT 
      id, name, category, description, market_demand,
      ts_rank(${searchVector}, ${searchQuery}) as rank
    FROM skills 
    WHERE ${searchVector} @@ ${searchQuery}
    ORDER BY rank DESC, market_demand DESC
    LIMIT ${options.limit || 10}
    OFFSET ${options.offset || 0}
  `;
  
  return {
    skills,
    totalCount: await getSearchCount(query),
    searchTime: Date.now() - startTime,
  };
}
```

**Connection Pooling:**
```typescript
// Prisma configuration for optimal connection pooling
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL + '?connection_limit=20&pool_timeout=20',
    },
  },
});
```

This technical documentation provides comprehensive coverage of the Skill Gap Analyzer's implementation details, architecture decisions, and best practices for maintenance and extension.
