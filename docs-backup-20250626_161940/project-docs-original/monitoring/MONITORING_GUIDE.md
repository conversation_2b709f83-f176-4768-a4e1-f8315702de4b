# Comprehensive Monitoring Guide - Skill Gap Analyzer

## Overview

This guide covers the comprehensive monitoring and logging system for the Skill Gap Analyzer feature. The monitoring system includes structured logging, real-time metrics collection, intelligent alerting, and performance tracking to ensure optimal system health and user experience.

## Architecture

### Core Components

1. **SkillGapLogger** - Structured logging with multiple transports
2. **SkillGapMetrics** - Real-time metrics collection and aggregation
3. **SkillGapAlerting** - Multi-channel alerting with escalation
4. **Monitoring Middleware** - Automatic API performance tracking
5. **Dashboard API** - Real-time monitoring dashboard

### Data Flow

```
API Request → Middleware → Logger/Metrics → Alerting → Dashboard
     ↓            ↓           ↓              ↓          ↓
Performance   Tracking   Aggregation   Notifications  Visualization
```

## Logging System

### Features

- **Structured Logging**: JSON-formatted logs with consistent schema
- **Multiple Transports**: Console (dev), File rotation (prod), External services
- **Log Levels**: Error, Warn, Info, Debug with environment-based filtering
- **Context Enrichment**: User ID, session ID, request ID, performance metrics
- **Daily Rotation**: Automatic log file rotation with retention policies

### Usage Examples

```typescript
import { skillGapLogger } from '@/lib/monitoring/logger';

// Log skill search
skillGapLogger.logSkillSearch({
  query: 'JavaScript',
  resultCount: 15,
  userId: 'user123',
  duration: 150,
  performanceMetrics: {
    responseTime: 150,
    memoryUsage: 45,
    cacheHit: true,
  },
});

// Log errors with context
skillGapLogger.logError(error, {
  feature: 'skill_assessment',
  action: 'submit_assessment',
  userId: 'user123',
  errorCode: 'VALIDATION_ERROR',
});

// Log security events
skillGapLogger.logSecurityEvent({
  eventType: 'suspicious_activity',
  severity: 'high',
  details: 'Multiple failed attempts',
  userId: 'user123',
});
```

### Log Categories

1. **Performance Logs**: API response times, throughput, resource usage
2. **Error Logs**: Application errors, validation failures, system exceptions
3. **Security Logs**: Authentication events, authorization failures, suspicious activity
4. **Business Logs**: User actions, feature usage, conversion metrics
5. **System Logs**: Health checks, resource monitoring, service status

## Metrics System

### Key Metrics

#### Performance Metrics
- **Response Time**: API endpoint response times (p50, p95, p99)
- **Throughput**: Requests per second, operations per minute
- **Error Rate**: Percentage of failed requests
- **Cache Hit Rate**: Cache effectiveness percentage

#### Business Metrics
- **Skill Searches**: Search volume, popular skills, result quality
- **Assessments**: Completion rate, average scores, time to complete
- **AI Usage**: Service calls, token consumption, success rate
- **User Engagement**: Active users, session duration, feature adoption

#### System Metrics
- **Memory Usage**: Heap usage, garbage collection frequency
- **CPU Usage**: Process CPU utilization
- **Database**: Connection pool, query performance
- **External Services**: AI service latency, third-party API health

### Usage Examples

```typescript
import { skillGapMetrics } from '@/lib/monitoring/metrics';

// Record skill search metrics
skillGapMetrics.recordSkillSearch(150, 5, true);

// Record AI service call
skillGapMetrics.recordAIServiceCall('gemini', 2000, 150, true);

// Get metric statistics
const stats = skillGapMetrics.getMetricStats('skill_search_duration', 3600000);
console.log(`Average response time: ${stats.avg}ms`);

// Get system health
const health = skillGapMetrics.getSystemHealth();
console.log(`System status: ${health.status}`);
```

## Alerting System

### Alert Types

1. **Performance Alerts**: Slow responses, high error rates, resource exhaustion
2. **Security Alerts**: Failed authentications, suspicious patterns, data breaches
3. **Business Alerts**: Low conversion rates, service degradation, user issues
4. **System Alerts**: Service outages, infrastructure problems, dependency failures

### Notification Channels

- **Console**: Development environment logging
- **Email**: Critical alerts to operations team
- **Slack**: Real-time team notifications
- **Webhook**: Integration with external monitoring systems
- **SMS**: Critical alerts for immediate attention

### Alert Configuration

```typescript
import { skillGapAlerting } from '@/lib/monitoring/alerting';

// Create custom alert
await skillGapAlerting.createAlert({
  title: 'High Error Rate',
  message: 'Skill search error rate exceeded 5%',
  severity: 'high',
  source: 'metrics_system',
  metadata: { errorRate: 7.5, threshold: 5.0 },
});

// Add notification channel
skillGapAlerting.addNotificationChannel({
  id: 'ops-email',
  type: 'email',
  config: {
    recipients: ['<EMAIL>'],
    smtpHost: 'smtp.company.com',
  },
  enabled: true,
  severityFilter: ['high', 'critical'],
});
```

### Escalation Rules

- **Critical**: Immediate notification + escalation after 5 minutes
- **High**: Notification + escalation after 15 minutes
- **Medium**: Notification + escalation after 1 hour
- **Low**: Notification only, no escalation

## Monitoring Middleware

### Automatic Tracking

The monitoring middleware automatically tracks:

- **Request Performance**: Response times, status codes, payload sizes
- **Error Handling**: Exception capture, error categorization, stack traces
- **User Behavior**: API usage patterns, feature adoption, user journeys
- **Security Events**: Authentication attempts, authorization failures

### Usage

```typescript
import { withSkillGapMonitoring } from '@/lib/monitoring/middleware';

// Wrap API handler with monitoring
export const GET = withSkillGapMonitoring(async (req: NextRequest) => {
  // Your API logic here
  return NextResponse.json({ success: true });
});

// Track custom operations
import { trackSkillGapOperation } from '@/lib/monitoring/middleware';

trackSkillGapOperation('complex_analysis', duration, {
  skillCount: 15,
  careerPath: 'Software Engineer',
});
```

## Dashboard API

### Endpoints

#### GET /api/monitoring/dashboard
Get comprehensive monitoring data

**Query Parameters:**
- `timeWindow`: Time window in milliseconds (default: 3600000)
- `includeAlerts`: Include alert data (default: false)
- `includeMetrics`: Include metrics data (default: true)

**Response:**
```json
{
  "timestamp": 1640995200000,
  "timeWindow": 3600000,
  "systemHealth": {
    "status": "healthy",
    "metrics": {...},
    "alerts": [...]
  },
  "performance": {
    "averageResponseTime": 245,
    "throughput": 15.2,
    "errorRate": 0.8,
    "cacheHitRate": 87.5
  }
}
```

#### POST /api/monitoring/dashboard
Perform monitoring actions

**Actions:**
- `acknowledge_alert`: Acknowledge an alert
- `resolve_alert`: Resolve an alert
- `test_notification`: Test notification channel
- `create_test_alert`: Create test alert

## Configuration

### Environment Variables

```bash
# Logging Configuration
NODE_ENV=production
LOG_LEVEL=info

# Alerting Configuration
ALERT_EMAIL_ENABLED=true
SMTP_HOST=smtp.company.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=password
ALERT_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>

# Slack Integration
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
SLACK_CHANNEL=#alerts

# Webhook Integration
ALERT_WEBHOOK_URL=https://monitoring.company.com/webhook
ALERT_WEBHOOK_TOKEN=secret-token

# Monitoring Thresholds
RESPONSE_TIME_THRESHOLD=2000
ERROR_RATE_THRESHOLD=5
MEMORY_THRESHOLD=80
CPU_THRESHOLD=80
```

### Alert Rules Configuration

```typescript
// Default alert rules
const alertRules = [
  {
    id: 'high_response_time',
    metricName: 'skill_search_duration',
    condition: 'gt',
    threshold: 2000,
    severity: 'high',
    cooldown: 300000, // 5 minutes
  },
  {
    id: 'high_error_rate',
    metricName: 'error_rate',
    condition: 'gt',
    threshold: 0.05, // 5%
    severity: 'critical',
    cooldown: 600000, // 10 minutes
  },
];
```

## Best Practices

### Logging Best Practices

1. **Structured Logging**: Always use structured JSON logs
2. **Context Enrichment**: Include user ID, session ID, request ID
3. **Sensitive Data**: Never log passwords, tokens, or PII
4. **Log Levels**: Use appropriate log levels (error, warn, info, debug)
5. **Performance**: Avoid excessive logging in hot paths

### Metrics Best Practices

1. **Meaningful Names**: Use descriptive metric names
2. **Consistent Units**: Standardize units (ms, count, percentage)
3. **Appropriate Granularity**: Balance detail with storage costs
4. **Retention Policies**: Define data retention based on importance
5. **Aggregation**: Pre-aggregate metrics for dashboard performance

### Alerting Best Practices

1. **Alert Fatigue**: Avoid too many low-priority alerts
2. **Actionable Alerts**: Ensure alerts have clear resolution steps
3. **Escalation**: Define clear escalation paths
4. **Documentation**: Document alert meanings and responses
5. **Testing**: Regularly test notification channels

## Troubleshooting

### Common Issues

1. **High Memory Usage**: Check for memory leaks in metrics collection
2. **Alert Spam**: Review alert thresholds and cooldown periods
3. **Missing Logs**: Verify log transport configuration
4. **Dashboard Slow**: Optimize metric queries and aggregation
5. **Notification Failures**: Test notification channel configuration

### Debugging Commands

```bash
# Test monitoring system
npm run test:monitoring

# Check log files
tail -f logs/skill-gap-$(date +%Y-%m-%d).log

# Test notification channels
curl -X POST /api/monitoring/dashboard \
  -H "Content-Type: application/json" \
  -d '{"action": "test_notification", "data": {"channelId": "slack"}}'
```

## Integration Examples

### Custom Metrics

```typescript
// Track custom business metric
skillGapMetrics.recordMetric({
  name: 'skill_gap_completion_rate',
  value: 85.5,
  unit: 'percentage',
  tags: { careerPath: 'Software Engineer' },
});
```

### Custom Alerts

```typescript
// Create business-specific alert
await skillGapAlerting.createAlert({
  title: 'Low Skill Assessment Completion',
  message: 'Completion rate dropped below 70%',
  severity: 'medium',
  source: 'business_metrics',
  metadata: { 
    currentRate: 65,
    threshold: 70,
    timeWindow: '1h',
  },
});
```

This comprehensive monitoring system ensures the Skill Gap Analyzer maintains optimal performance, reliability, and user experience through proactive monitoring, intelligent alerting, and detailed observability.
