# Skill Gap Analyzer - Deployment Guide

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Database Setup](#database-setup)
4. [Application Configuration](#application-configuration)
5. [Deployment Process](#deployment-process)
6. [Monitoring Setup](#monitoring-setup)
7. [Backup & Recovery](#backup--recovery)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

**Development Environment:**
- Node.js 18.17+ or 20.5+
- npm 9+ or yarn 1.22+
- Git 2.30+
- PostgreSQL 14+ (local development)
- Redis 6+ (optional for local development)

**Production Environment:**
- Vercel account (recommended) or Docker-compatible hosting
- Neon PostgreSQL database or compatible PostgreSQL 14+
- Upstash Redis or compatible Redis 6+
- Google Cloud account (for Gemini AI)
- Resend account (for email notifications)
- Sentry account (for error monitoring)

### Required Accounts & Services

1. **Vercel** - Application hosting and deployment
2. **Neon** - PostgreSQL database hosting
3. **Upstash** - Redis hosting for caching
4. **Google Cloud** - Gemini AI API access
5. **Resend** - Email service for notifications
6. **Sentry** - Error monitoring and performance tracking

## Environment Setup

### Local Development

1. **Clone Repository**
   ```bash
   git clone https://github.com/your-org/faafo-career-platform.git
   cd faafo-career-platform
   ```

2. **Install Dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Environment Variables**
   Create `.env.local` file:
   ```bash
   # Database
   DATABASE_URL="postgresql://username:password@localhost:5432/faafo_dev"
   
   # NextAuth.js
   NEXTAUTH_URL="http://localhost:3000"
   NEXTAUTH_SECRET="your-secret-key-here"
   
   # AI Services
   GEMINI_API_KEY="your-gemini-api-key"
   
   # Email Service
   RESEND_API_KEY="your-resend-api-key"
   
   # Redis (optional for local dev)
   REDIS_URL="redis://localhost:6379"
   
   # Monitoring
   SENTRY_DSN="your-sentry-dsn"
   
   # Feature Flags
   ENABLE_AI_ANALYSIS="true"
   ENABLE_CACHING="true"
   ENABLE_MONITORING="true"
   ```

4. **Database Setup**
   ```bash
   # Generate Prisma client
   npx prisma generate
   
   # Run database migrations
   npx prisma db push
   
   # Seed database (optional)
   npm run db:seed
   ```

5. **Start Development Server**
   ```bash
   npm run dev
   ```

### Production Environment Variables

```bash
# Core Application
NODE_ENV="production"
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="production-secret-key"

# Database
DATABASE_URL="********************************/dbname?sslmode=require"

# AI Services
GEMINI_API_KEY="production-gemini-key"

# Email Service
RESEND_API_KEY="production-resend-key"

# Redis Cache
REDIS_URL="redis://user:pass@host:port"

# Monitoring & Logging
SENTRY_DSN="production-sentry-dsn"
LOG_LEVEL="info"

# Performance
ENABLE_CACHING="true"
CACHE_TTL="3600"

# Security
CORS_ORIGINS="https://your-domain.com"
RATE_LIMIT_ENABLED="true"

# Feature Flags
ENABLE_AI_ANALYSIS="true"
ENABLE_MONITORING="true"
ENABLE_ANALYTICS="true"
```

## Database Setup

### Neon PostgreSQL Setup

1. **Create Neon Project**
   - Visit [Neon Console](https://console.neon.tech)
   - Create new project
   - Select region closest to your users
   - Note the connection string

2. **Configure Database**
   ```sql
   -- Enable required extensions
   CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
   CREATE EXTENSION IF NOT EXISTS "pg_trgm";
   
   -- Create indexes for performance
   CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_skills_search 
   ON skills USING gin(to_tsvector('english', name || ' ' || description));
   ```

3. **Run Migrations**
   ```bash
   # Set production database URL
   export DATABASE_URL="your-neon-connection-string"
   
   # Run migrations
   npx prisma migrate deploy
   
   # Generate client
   npx prisma generate
   ```

### Database Seeding

```bash
# Seed with initial data
npm run db:seed:production

# Verify seeding
npx prisma studio
```

### Backup Configuration

```bash
# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups/$(date +%Y-%m-%d)"
mkdir -p $BACKUP_DIR

pg_dump $DATABASE_URL > $BACKUP_DIR/faafo_backup.sql
gzip $BACKUP_DIR/faafo_backup.sql

# Upload to cloud storage
aws s3 cp $BACKUP_DIR/faafo_backup.sql.gz s3://your-backup-bucket/
```

## Application Configuration

### Vercel Deployment

1. **Connect Repository**
   - Import project in Vercel dashboard
   - Connect to GitHub repository
   - Configure build settings

2. **Build Configuration**
   ```json
   {
     "buildCommand": "npm run build",
     "outputDirectory": ".next",
     "installCommand": "npm ci",
     "framework": "nextjs"
   }
   ```

3. **Environment Variables**
   Add all production environment variables in Vercel dashboard

4. **Domain Configuration**
   - Add custom domain
   - Configure DNS records
   - Enable SSL certificate

### Docker Deployment (Alternative)

1. **Dockerfile**
   ```dockerfile
   FROM node:18-alpine AS base
   
   # Install dependencies
   FROM base AS deps
   WORKDIR /app
   COPY package.json package-lock.json ./
   RUN npm ci --only=production
   
   # Build application
   FROM base AS builder
   WORKDIR /app
   COPY . .
   COPY --from=deps /app/node_modules ./node_modules
   RUN npm run build
   
   # Production image
   FROM base AS runner
   WORKDIR /app
   
   ENV NODE_ENV production
   
   RUN addgroup --system --gid 1001 nodejs
   RUN adduser --system --uid 1001 nextjs
   
   COPY --from=builder /app/public ./public
   COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
   COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
   
   USER nextjs
   
   EXPOSE 3000
   
   ENV PORT 3000
   
   CMD ["node", "server.js"]
   ```

2. **Docker Compose**
   ```yaml
   version: '3.8'
   services:
     app:
       build: .
       ports:
         - "3000:3000"
       environment:
         - DATABASE_URL=${DATABASE_URL}
         - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
         - GEMINI_API_KEY=${GEMINI_API_KEY}
       depends_on:
         - postgres
         - redis
   
     postgres:
       image: postgres:14
       environment:
         POSTGRES_DB: faafo
         POSTGRES_USER: postgres
         POSTGRES_PASSWORD: password
       volumes:
         - postgres_data:/var/lib/postgresql/data
   
     redis:
       image: redis:6-alpine
       volumes:
         - redis_data:/data
   
   volumes:
     postgres_data:
     redis_data:
   ```

## Deployment Process

### Automated Deployment (Recommended)

1. **GitHub Actions Workflow**
   ```yaml
   name: Deploy to Production
   
   on:
     push:
       branches: [main]
   
   jobs:
     test:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v3
         - uses: actions/setup-node@v3
           with:
             node-version: '18'
         - run: npm ci
         - run: npm run test
         - run: npm run test:security
         - run: npm run test:performance
   
     deploy:
       needs: test
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v3
         - uses: amondnet/vercel-action@v20
           with:
             vercel-token: ${{ secrets.VERCEL_TOKEN }}
             vercel-org-id: ${{ secrets.ORG_ID }}
             vercel-project-id: ${{ secrets.PROJECT_ID }}
             vercel-args: '--prod'
   ```

2. **Pre-deployment Checks**
   ```bash
   # Run all tests
   npm run test:all
   
   # Security audit
   npm audit --audit-level high
   
   # Performance validation
   npm run test:performance
   
   # Build verification
   npm run build
   ```

### Manual Deployment

1. **Pre-deployment**
   ```bash
   # Backup current database
   npm run db:backup
   
   # Run migrations (if any)
   npx prisma migrate deploy
   
   # Build application
   npm run build
   ```

2. **Deploy to Vercel**
   ```bash
   # Install Vercel CLI
   npm i -g vercel
   
   # Deploy
   vercel --prod
   ```

3. **Post-deployment Verification**
   ```bash
   # Health check
   curl https://your-domain.com/api/health
   
   # Smoke tests
   npm run test:smoke
   ```

## Monitoring Setup

### Application Monitoring

1. **Sentry Configuration**
   ```typescript
   // sentry.client.config.ts
   import * as Sentry from "@sentry/nextjs";
   
   Sentry.init({
     dsn: process.env.SENTRY_DSN,
     tracesSampleRate: 1.0,
     environment: process.env.NODE_ENV,
   });
   ```

2. **Custom Monitoring Dashboard**
   ```bash
   # Access monitoring dashboard
   https://your-domain.com/api/monitoring/dashboard
   ```

3. **Alerts Configuration**
   ```typescript
   // Configure alerts for:
   // - High error rates (>5%)
   // - Slow response times (>2s)
   // - AI service failures
   // - Database connection issues
   ```

### Performance Monitoring

1. **Core Web Vitals**
   - Largest Contentful Paint (LCP) < 2.5s
   - First Input Delay (FID) < 100ms
   - Cumulative Layout Shift (CLS) < 0.1

2. **API Performance**
   - Skill search: < 500ms
   - Assessment submission: < 1s
   - AI analysis: < 10s

3. **Database Performance**
   - Query response time < 100ms
   - Connection pool utilization < 80%

### Health Checks

```typescript
// /api/health endpoint
export async function GET() {
  const checks = await Promise.allSettled([
    checkDatabase(),
    checkRedis(),
    checkAIService(),
    checkEmailService(),
  ]);
  
  const status = checks.every(check => check.status === 'fulfilled') 
    ? 'healthy' 
    : 'unhealthy';
  
  return NextResponse.json({
    status,
    timestamp: new Date().toISOString(),
    checks: checks.map((check, index) => ({
      service: ['database', 'redis', 'ai', 'email'][index],
      status: check.status,
    })),
  });
}
```

## Backup & Recovery

### Database Backup Strategy

1. **Automated Daily Backups**
   ```bash
   # Cron job for daily backups
   0 2 * * * /scripts/backup-database.sh
   ```

2. **Point-in-Time Recovery**
   ```bash
   # Neon provides automatic point-in-time recovery
   # Recovery window: 7 days for free tier, 30 days for paid
   ```

3. **Backup Verification**
   ```bash
   # Test backup restoration monthly
   npm run test:backup-restore
   ```

### Disaster Recovery Plan

1. **RTO (Recovery Time Objective)**: 4 hours
2. **RPO (Recovery Point Objective)**: 1 hour
3. **Recovery Steps**:
   - Restore database from latest backup
   - Deploy application from last known good commit
   - Verify all services are operational
   - Notify users of service restoration

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   ```bash
   # Check connection string
   npx prisma db pull
   
   # Verify SSL configuration
   psql "$DATABASE_URL" -c "SELECT version();"
   ```

2. **AI Service Timeouts**
   ```bash
   # Check API key validity
   curl -H "Authorization: Bearer $GEMINI_API_KEY" \
        https://generativelanguage.googleapis.com/v1/models
   
   # Monitor rate limits
   npm run monitor:ai-usage
   ```

3. **Memory Issues**
   ```bash
   # Monitor memory usage
   node --max-old-space-size=4096 server.js
   
   # Analyze memory leaks
   npm run analyze:memory
   ```

4. **Performance Degradation**
   ```bash
   # Check database performance
   npm run analyze:db-performance
   
   # Monitor cache hit rates
   npm run monitor:cache
   ```

### Debug Commands

```bash
# Application logs
vercel logs --follow

# Database queries
npx prisma studio

# Performance analysis
npm run analyze:performance

# Security scan
npm run security:scan

# Dependency audit
npm audit --audit-level moderate
```

### Support Contacts

- **Technical Issues**: <EMAIL>
- **Database Issues**: <EMAIL>
- **Security Issues**: <EMAIL>
- **Emergency**: +1-XXX-XXX-XXXX

---

*Last Updated: January 2024*
*Version: 2.0*
