'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  Legend,
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, DollarSign, BarChart3, Clock } from 'lucide-react';

interface MarketData {
  skill: string;
  demand: number;
  supply: number;
  averageSalary: number;
  growth: number;
  difficulty: number;
  timeToLearn: number;
  category: string;
}

interface MarketInsightsChartProps {
  data: MarketData[];
  title?: string;
  description?: string;
  height?: number;
  showLegend?: boolean;
  metric?: 'demand-supply' | 'salary' | 'growth' | 'difficulty';
  sortBy?: 'demand' | 'supply' | 'averageSalary' | 'growth' | 'difficulty';
  filterCategory?: string;
  colors?: {
    demand?: string;
    supply?: string;
    salary?: string;
    growth?: string;
  };
}

export default function MarketInsightsChart({
  data,
  title = "Market Insights",
  description = "Skill demand, supply, and market trends",
  height = 400,
  showLegend = true,
  metric = 'demand-supply',
  sortBy,
  filterCategory,
  colors = {
    demand: '#3b82f6',
    supply: '#10b981',
    salary: '#f59e0b',
    growth: '#8b5cf6',
  },
}: MarketInsightsChartProps) {
  const [selectedMetric, setSelectedMetric] = useState(metric);

  // Filter and sort data
  let processedData = [...data];
  
  if (filterCategory) {
    processedData = processedData.filter(item => item.category === filterCategory);
  }
  
  if (sortBy) {
    processedData.sort((a, b) => b[sortBy] - a[sortBy]);
  }

  // Calculate statistics
  const safeAverage = (values: number[]) => {
    if (values.length === 0) return 0;
    return values.reduce((sum, value) => sum + value, 0) / values.length;
  };

  const safeMax = (values: number[]) => {
    if (values.length === 0) return 0;
    return Math.max(...values);
  };

  const safeMin = (values: number[]) => {
    if (values.length === 0) return 0;
    return Math.min(...values);
  };

  const avgDemand = safeAverage(data.map(item => item.demand));
  const avgSupply = safeAverage(data.map(item => item.supply));
  const marketGap = avgDemand - avgSupply;
  
  const avgSalary = safeAverage(data.map(item => item.averageSalary));
  const highestSalary = safeMax(data.map(item => item.averageSalary));
  const lowestSalary = safeMin(data.map(item => item.averageSalary));
  
  const avgGrowth = safeAverage(data.map(item => item.growth));
  const highestGrowth = safeMax(data.map(item => item.growth));
  const lowestGrowth = safeMin(data.map(item => item.growth));

  // Get chart configuration based on selected metric
  const getChartConfig = () => {
    switch (selectedMetric) {
      case 'salary':
        return {
          title: 'Average Salary',
          bars: [{ key: 'averageSalary', name: 'Salary', color: colors.salary }],
          yAxisFormatter: (value: number) => `$${(value / 1000).toFixed(0)}k`,
        };
      case 'growth':
        return {
          title: 'Growth Rate',
          bars: [{ key: 'growth', name: 'Growth %', color: colors.growth }],
          yAxisFormatter: (value: number) => `${value}%`,
        };
      case 'difficulty':
        return {
          title: 'Learning Difficulty',
          bars: [{ key: 'difficulty', name: 'Difficulty', color: '#ef4444' }],
          yAxisFormatter: (value: number) => value.toString(),
        };
      default:
        return {
          title: 'Demand vs Supply',
          bars: [
            { key: 'demand', name: 'Demand', color: colors.demand },
            { key: 'supply', name: 'Supply', color: colors.supply },
          ],
          yAxisFormatter: (value: number) => value.toString(),
        };
    }
  };

  const chartConfig = getChartConfig();

  // Get top skills for recommendations
  const getTopSkills = (sortKey: 'demand' | 'supply' | 'averageSalary' | 'growth' | 'difficulty', limit = 3) => {
    return [...processedData]
      .sort((a, b) => (b[sortKey] as number) - (a[sortKey] as number))
      .slice(0, limit);
  };

  const formatSalary = (salary: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(salary);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          {title}
        </CardTitle>
        {description && (
          <CardDescription>{description}</CardDescription>
        )}
        
        {/* Metric Selection */}
        <div className="flex flex-wrap gap-2 mt-4">
          <Button
            variant={selectedMetric === 'demand-supply' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedMetric('demand-supply')}
          >
            <TrendingUp className="h-4 w-4 mr-1" />
            Demand vs Supply
          </Button>
          <Button
            variant={selectedMetric === 'salary' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedMetric('salary')}
          >
            <DollarSign className="h-4 w-4 mr-1" />
            Salary
          </Button>
          <Button
            variant={selectedMetric === 'growth' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedMetric('growth')}
          >
            <TrendingUp className="h-4 w-4 mr-1" />
            Growth
          </Button>
          <Button
            variant={selectedMetric === 'difficulty' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedMetric('difficulty')}
          >
            <Clock className="h-4 w-4 mr-1" />
            Difficulty
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Summary Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {selectedMetric === 'demand-supply' && (
            <>
              <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="text-sm font-medium text-blue-700 dark:text-blue-300">
                  Avg Demand
                </div>
                <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                  {avgDemand.toFixed(1)}
                </div>
              </div>
              <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="text-sm font-medium text-green-700 dark:text-green-300">
                  Avg Supply
                </div>
                <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                  {avgSupply.toFixed(1)}
                </div>
              </div>
              <div className="text-center p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                <div className="text-sm font-medium text-amber-700 dark:text-amber-300">
                  Market Gap
                </div>
                <div className="text-2xl font-bold text-amber-900 dark:text-amber-100">
                  {marketGap.toFixed(1)}
                </div>
              </div>
            </>
          )}
          
          {selectedMetric === 'salary' && (
            <>
              <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="text-sm font-medium text-blue-700 dark:text-blue-300">
                  Avg Salary
                </div>
                <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                  {formatSalary(avgSalary)}
                </div>
              </div>
              <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="text-sm font-medium text-green-700 dark:text-green-300">
                  Highest
                </div>
                <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                  {formatSalary(highestSalary)}
                </div>
              </div>
              <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <div className="text-sm font-medium text-red-700 dark:text-red-300">
                  Lowest
                </div>
                <div className="text-2xl font-bold text-red-900 dark:text-red-100">
                  {formatSalary(lowestSalary)}
                </div>
              </div>
            </>
          )}
          
          {selectedMetric === 'growth' && (
            <>
              <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="text-sm font-medium text-blue-700 dark:text-blue-300">
                  Avg Growth
                </div>
                <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                  {avgGrowth.toFixed(1)}%
                </div>
              </div>
              <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="text-sm font-medium text-green-700 dark:text-green-300">
                  Highest
                </div>
                <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                  {highestGrowth.toFixed(1)}%
                </div>
              </div>
              <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <div className="text-sm font-medium text-red-700 dark:text-red-300">
                  Lowest
                </div>
                <div className="text-2xl font-bold text-red-900 dark:text-red-100">
                  {lowestGrowth.toFixed(1)}%
                </div>
              </div>
            </>
          )}
        </div>

        {/* Chart */}
        <div>
          <h3 className="text-lg font-semibold mb-4">{chartConfig.title}</h3>
          <div style={{ width: '100%', height }}>
            <ResponsiveContainer>
              <BarChart data={processedData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-gray-200 dark:stroke-gray-700" />
                <XAxis 
                  dataKey="skill" 
                  tick={{ fontSize: 12 }}
                  className="fill-gray-600 dark:fill-gray-400"
                />
                <YAxis 
                  tickFormatter={chartConfig.yAxisFormatter}
                  tick={{ fontSize: 12 }}
                  className="fill-gray-600 dark:fill-gray-400"
                />
                <Tooltip
                  formatter={(value: number, name: string) => [
                    chartConfig.yAxisFormatter(value),
                    name
                  ]}
                  contentStyle={{
                    backgroundColor: 'var(--background)',
                    border: '1px solid var(--border)',
                    borderRadius: '6px',
                    color: 'var(--foreground)',
                  }}
                />
                {showLegend && <Legend />}
                
                {chartConfig.bars.map((bar) => (
                  <Bar
                    key={bar.key}
                    dataKey={bar.key}
                    fill={bar.color}
                    name={bar.name}
                  />
                ))}
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Skill Recommendations */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {selectedMetric === 'demand-supply' && (
            <>
              <div>
                <h4 className="font-semibold mb-3">High Demand Skills</h4>
                <div className="space-y-2">
                  {getTopSkills('demand').map((skill) => (
                    <div key={skill.skill} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-900/20 rounded">
                      <span className="font-medium">{skill.skill}</span>
                      <Badge variant="secondary">{skill.demand}% demand</Badge>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-semibold mb-3">Market Opportunities</h4>
                <div className="space-y-2">
                  {processedData
                    .filter(skill => skill.demand > skill.supply)
                    .sort((a, b) => (b.demand - b.supply) - (a.demand - a.supply))
                    .slice(0, 3)
                    .map((skill) => (
                      <div key={skill.skill} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-900/20 rounded">
                        <span className="font-medium">{skill.skill}</span>
                        <Badge variant="secondary">+{(skill.demand - skill.supply).toFixed(1)} gap</Badge>
                      </div>
                    ))}
                </div>
              </div>
            </>
          )}
          
          {selectedMetric === 'salary' && (
            <div>
              <h4 className="font-semibold mb-3">Highest Paying</h4>
              <div className="space-y-2">
                {getTopSkills('averageSalary').map((skill) => (
                  <div key={skill.skill} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-900/20 rounded">
                    <span className="font-medium">{skill.skill}</span>
                    <Badge variant="secondary">{formatSalary(skill.averageSalary)}</Badge>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {selectedMetric === 'growth' && (
            <div>
              <h4 className="font-semibold mb-3">Fastest Growing</h4>
              <div className="space-y-2">
                {getTopSkills('growth').map((skill) => (
                  <div key={skill.skill} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-900/20 rounded">
                    <span className="font-medium">{skill.skill}</span>
                    <Badge variant="secondary">{skill.growth.toFixed(1)}% growth</Badge>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
