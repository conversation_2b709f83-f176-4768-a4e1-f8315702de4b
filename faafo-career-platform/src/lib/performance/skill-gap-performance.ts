/**
 * Performance monitoring and caching strategies for Skill Gap Analyzer
 * Implements comprehensive performance tracking and optimization
 */

import { PerformanceMonitor } from '@/lib/performance-monitoring';
import { AIServiceMonitor } from '@/lib/ai-service-monitor';
import { cacheService } from '@/lib/services/cacheService';

interface SkillSearchMetrics {
  query: string;
  resultCount: number;
  responseTime: number;
  cacheHit: boolean;
  userId?: string;
}

interface SkillAssessmentMetrics {
  assessmentCount: number;
  responseTime: number;
  success: boolean;
  userId: string;
}

interface SkillAnalysisMetrics {
  analysisType: 'basic' | 'comprehensive';
  skillCount: number;
  responseTime: number;
  success: boolean;
  cacheHit: boolean;
  userId: string;
  error?: string;
}

export class SkillGapPerformanceMonitor {
  private performanceMonitor: PerformanceMonitor | null;
  private aiServiceMonitor: AIServiceMonitor | null;
  private cacheService: typeof cacheService;

  // Cache TTL configurations (in seconds)
  private readonly CACHE_TTL = {
    SKILL_SEARCH: 300,        // 5 minutes
    USER_ASSESSMENTS: 3600,   // 1 hour
    SKILL_ANALYSIS: 1800,     // 30 minutes
    POPULAR_SKILLS: 7200,     // 2 hours
    MARKET_DATA: 86400,       // 24 hours
  };

  // Performance thresholds
  private readonly THRESHOLDS = {
    SKILL_SEARCH_WARNING: 1000,      // 1 second
    SKILL_SEARCH_CRITICAL: 3000,     // 3 seconds
    ANALYSIS_WARNING: 5000,          // 5 seconds
    ANALYSIS_CRITICAL: 15000,        // 15 seconds
    CACHE_HIT_RATE_WARNING: 70,      // 70%
    CACHE_HIT_RATE_CRITICAL: 50,     // 50%
  };

  constructor() {
    // Only initialize monitoring in production
    if (process.env.NODE_ENV === 'production') {
      this.performanceMonitor = new PerformanceMonitor();
      this.aiServiceMonitor = AIServiceMonitor.getInstance();
    } else {
      this.performanceMonitor = null;
      this.aiServiceMonitor = null;
    }
    this.cacheService = cacheService;
  }

  /**
   * Monitor skill search performance
   */
  async monitorSkillSearch(
    query: string,
    operation: () => Promise<any>,
    userId?: string
  ): Promise<any> {
    const startTime = Date.now();
    const cacheKey = `skill_search:${query.toLowerCase()}`;
    
    try {
      // Check cache first
      const cachedResult = await this.cacheService.getJSON(cacheKey);
      if (cachedResult) {
        const responseTime = Date.now() - startTime;
        this.recordSkillSearchMetrics({
          query,
          resultCount: Array.isArray(cachedResult) ? cachedResult.length : 0,
          responseTime,
          cacheHit: true,
          userId
        });
        return cachedResult;
      }

      // Execute operation
      const result = await operation();
      const responseTime = Date.now() - startTime;

      // Cache the result
      await this.cacheService.setJSON(cacheKey, result, this.CACHE_TTL.SKILL_SEARCH);

      // Record metrics
      this.recordSkillSearchMetrics({
        query,
        resultCount: result.length || 0,
        responseTime,
        cacheHit: false,
        userId
      });

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.recordSkillSearchError(query, responseTime, error as Error, userId);
      throw error;
    }
  }

  /**
   * Monitor skill assessment submission performance
   */
  async monitorSkillAssessment(
    assessments: any[],
    operation: () => Promise<any>,
    userId: string
  ): Promise<any> {
    const startTime = Date.now();
    
    try {
      const result = await operation();
      const responseTime = Date.now() - startTime;

      // Cache user assessments
      const cacheKey = `user_assessments:${userId}`;
      await this.cacheService.setJSON(cacheKey, assessments, this.CACHE_TTL.USER_ASSESSMENTS);

      // Record metrics
      this.recordSkillAssessmentMetrics({
        assessmentCount: assessments.length,
        responseTime,
        success: true,
        userId
      });

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.recordSkillAssessmentMetrics({
        assessmentCount: assessments.length,
        responseTime,
        success: false,
        userId
      });
      throw error;
    }
  }

  /**
   * Monitor comprehensive skill analysis performance
   */
  async monitorSkillAnalysis(
    analysisRequest: any,
    operation: () => Promise<any>,
    userId: string
  ): Promise<any> {
    const startTime = Date.now();
    const analysisId = this.generateAnalysisId(analysisRequest, userId);
    const cacheKey = `skill_analysis:${analysisId}`;
    
    try {
      // Check cache first
      const cachedResult = await this.cacheService.getJSON(cacheKey);
      if (cachedResult) {
        const responseTime = Date.now() - startTime;
        this.recordSkillAnalysisMetrics({
          analysisType: 'comprehensive',
          skillCount: analysisRequest.currentSkills?.length || 0,
          responseTime,
          success: true,
          cacheHit: true,
          userId
        });
        return { ...cachedResult, cached: true };
      }

      // Execute operation
      const result = await operation();
      const responseTime = Date.now() - startTime;

      // Cache the result
      await this.cacheService.setJSON(cacheKey, result, this.CACHE_TTL.SKILL_ANALYSIS);

      // Record metrics
      this.recordSkillAnalysisMetrics({
        analysisType: 'comprehensive',
        skillCount: analysisRequest.currentSkills?.length || 0,
        responseTime,
        success: true,
        cacheHit: false,
        userId
      });

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.recordSkillAnalysisMetrics({
        analysisType: 'comprehensive',
        skillCount: analysisRequest.currentSkills?.length || 0,
        responseTime,
        success: false,
        cacheHit: false,
        userId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Warm cache with popular skills
   */
  async warmPopularSkillsCache(): Promise<void> {
    const popularSkills = [
      'JavaScript', 'Python', 'React', 'Node.js', 'TypeScript',
      'Java', 'C++', 'SQL', 'AWS', 'Docker'
    ];

    const warmingPromises = popularSkills.map(async (skill) => {
      const cacheKey = `skill_data:${skill.toLowerCase()}`;
      const skillData = {
        name: skill,
        popular: true,
        warmedAt: Date.now()
      };
      await this.cacheService.setJSON(cacheKey, skillData, this.CACHE_TTL.POPULAR_SKILLS);
    });

    await Promise.all(warmingPromises);
    console.log(`🔥 Warmed cache for ${popularSkills.length} popular skills`);
  }

  /**
   * Get performance status for skill gap analyzer
   */
  getPerformanceStatus(): any {
    if (!this.performanceMonitor) return { isHealthy: true, message: 'Monitoring disabled in development' };
    return this.performanceMonitor.getPerformanceStatus();
  }

  /**
   * Get AI service metrics
   */
  getAIServiceMetrics(): any {
    if (!this.aiServiceMonitor) return { message: 'AI monitoring disabled in development' };
    return this.aiServiceMonitor.getMetrics();
  }

  /**
   * Perform health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const cacheHealthy = await this.cacheService.healthCheck();
      const performanceStatus = this.getPerformanceStatus();

      return cacheHealthy && performanceStatus.isHealthy;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }

  // Private helper methods

  private recordSkillSearchMetrics(metrics: SkillSearchMetrics): void {
    if (!this.performanceMonitor) return; // Skip in development

    this.performanceMonitor.recordOperation('skill_search', metrics.responseTime, true);

    if (metrics.responseTime > this.THRESHOLDS.SKILL_SEARCH_WARNING) {
      console.warn(`⚠️ Slow skill search: ${metrics.query} took ${metrics.responseTime}ms`);
    }
  }

  private recordSkillSearchError(query: string, responseTime: number, error: Error, userId?: string): void {
    if (!this.performanceMonitor) return; // Skip in development

    this.performanceMonitor.recordOperation('skill_search', responseTime, false);
  }

  private recordSkillAssessmentMetrics(metrics: SkillAssessmentMetrics): void {
    if (!this.aiServiceMonitor) return; // Skip in development

    this.aiServiceMonitor.recordOperation(
      'skill_assessment_submit',
      metrics.responseTime,
      metrics.success,
      false,
      metrics.userId
    );
  }

  private recordSkillAnalysisMetrics(metrics: SkillAnalysisMetrics): void {
    if (!this.aiServiceMonitor) return; // Skip in development

    this.aiServiceMonitor.recordOperation(
      'comprehensive_skill_analysis',
      metrics.responseTime,
      metrics.success,
      metrics.cacheHit,
      metrics.userId,
      metrics.error
    );

    if (metrics.responseTime > this.THRESHOLDS.ANALYSIS_WARNING) {
      console.warn(`⚠️ Slow skill analysis for user ${metrics.userId}: ${metrics.responseTime}ms`);
    }
  }

  private generateAnalysisId(analysisRequest: any, userId: string): string {
    const skillNames = analysisRequest.currentSkills?.map((s: any) => s.skillName).sort().join(',') || '';
    const careerPath = analysisRequest.targetCareerPath?.careerPathName || '';
    const level = analysisRequest.targetCareerPath?.targetLevel || '';
    
    const hash = Buffer.from(`${userId}:${skillNames}:${careerPath}:${level}`).toString('base64');
    return hash.substring(0, 16);
  }
}

// Export singleton instance
export const skillGapPerformanceMonitor = new SkillGapPerformanceMonitor();
