/**
 * Skill Gap Analyzer Error Tracking
 * Specialized error tracking for skill gap analyzer functionality
 * Integrates with existing Sentry infrastructure
 */

import { errorTracker, trackError } from '@/lib/errorTracking';
import { ErrorReporter } from '@/lib/errorReporting';

interface SkillSearchErrorContext {
  userId: string;
  searchQuery: string;
  searchFilters?: Record<string, any>;
  responseTime?: number;
  retryAttempt?: number;
  timeoutDuration?: number;
}

interface SkillSearchCacheMissData {
  searchQuery: string;
  userId: string;
  cacheKey: string;
  missReason: string;
  searchFrequency: string;
}

interface AssessmentSubmissionErrorContext {
  userId: string;
  assessmentData: {
    skillCount: number;
    assessmentType: string;
    totalTime: number;
  };
  validationErrors?: string[];
  submissionAttempt: number;
}

interface AssessmentValidationErrorContext {
  userId: string;
  field: string;
  value: any;
  skillName: string;
  expectedRange?: [number, number];
}

interface AIServiceErrorContext {
  userId: string;
  analysisRequest: {
    skillCount: number;
    targetCareerPath: string;
    analysisType: string;
  };
  aiProvider: string;
  requestId: string;
  rateLimitInfo?: {
    limit: number;
    remaining: number;
    resetTime: number;
  };
}

interface AIResponseQualityIssue {
  userId: string;
  requestId: string;
  analysisType: string;
  qualityMetrics: {
    responseCompleteness: number;
    responseRelevance: number;
    responseAccuracy: number;
  };
  expectedQuality: {
    completeness: number;
    relevance: number;
    accuracy: number;
  };
  responseData: Record<string, any>;
}

interface PerformanceData {
  operation: string;
  userId: string;
  responseTime: number;
  threshold: number;
  skillCount?: number;
  analysisComplexity?: string;
  cacheHit?: boolean;
}

interface MemoryData {
  operation: string;
  userId: string;
  memoryUsage: number;
  memoryLimit: number;
  assessmentCount?: number;
  processingTime?: number;
}

interface UserWorkflowAbandonmentData {
  userId: string;
  workflowType: string;
  abandonmentStage: string;
  timeSpent: number;
  completionPercentage: number;
  lastAction: string;
  sessionData: {
    deviceType: string;
    browserType: string;
    connectionSpeed: string;
  };
}

interface FormValidationFrustrationData {
  userId: string;
  formType: string;
  validationErrors: Array<{
    field: string;
    attempts: number;
    errorType: string;
  }>;
  totalAttempts: number;
  timeSpent: number;
  userAgent?: string;
}

interface WorkflowStep {
  userId: string;
  step: string;
  data: Record<string, any>;
}

interface UserContext {
  userId: string;
  email?: string;
  skillLevel?: string;
  careerPath?: string;
  analysisHistory?: {
    totalAnalyses: number;
    lastAnalysis: string;
    averageSkillCount: number;
  };
}

interface TimeRange {
  startDate: Date;
  endDate: Date;
}

interface ErrorSummary {
  timeRange: TimeRange;
  totalErrors: number;
  errorsByType: Record<string, number>;
  topErrors: Array<{ error: string; count: number; impact: string }>;
  affectedUsers: number;
  performanceIssues: number;
  qualityIssues: number;
  recommendations: string[];
  trends: Record<string, any>;
}

interface ErrorPatternAnalysis {
  commonErrorPatterns: Array<{ pattern: string; frequency: number; impact: string }>;
  userSegmentImpacts: Record<string, any>;
  timeBasedTrends: Record<string, any>;
  correlationInsights: Array<{ correlation: string; strength: number }>;
  preventionStrategies: string[];
  priorityActions: string[];
}

export class SkillGapErrorTracker {
  /**
   * Track skill search API errors
   */
  async trackSkillSearchError(error: Error, context: SkillSearchErrorContext): Promise<void> {
    // Track as API error
    trackError.api(error, '/api/skills/search', 'GET', 500);

    // Track with detailed context
    errorTracker.captureException(error, {
      tags: {
        errorType: 'skill_search',
        searchQuery: context.searchQuery,
        category: context.searchFilters?.category || 'unknown',
        slowResponse: context.responseTime && context.responseTime > 3000 ? 'true' : 'false',
      },
      extra: {
        userId: context.userId,
        searchQuery: context.searchQuery,
        searchFilters: context.searchFilters,
        responseTime: context.responseTime,
        timestamp: new Date().toISOString(),
      },
      user: {
        id: context.userId,
      },
    });
  }

  /**
   * Track skill search timeout errors
   */
  async trackSkillSearchTimeout(error: Error, context: SkillSearchErrorContext): Promise<void> {
    errorTracker.captureException(error, {
      tags: {
        errorType: 'skill_search_timeout',
        searchQuery: context.searchQuery,
        retryAttempt: context.retryAttempt?.toString() || '0',
        severity: 'high',
      },
      extra: {
        userId: context.userId,
        searchQuery: context.searchQuery,
        timeoutDuration: context.timeoutDuration,
        retryAttempt: context.retryAttempt,
        recommendedAction: 'retry_with_simpler_query',
      },
      user: {
        id: context.userId,
      },
    });
  }

  /**
   * Track skill search cache miss patterns
   */
  async trackSkillSearchCacheMiss(data: SkillSearchCacheMissData): Promise<void> {
    errorTracker.captureMessage('Skill search cache miss for high-frequency query', {
      level: 'warning',
      tags: {
        errorType: 'cache_miss',
        searchQuery: data.searchQuery,
        missReason: data.missReason,
        frequency: data.searchFrequency,
      },
      extra: {
        userId: data.userId,
        cacheKey: data.cacheKey,
        searchFrequency: data.searchFrequency,
        optimizationSuggestion: 'increase_cache_ttl',
      },
    });
  }

  /**
   * Track skill assessment submission errors
   */
  async trackAssessmentSubmissionError(error: Error, context: AssessmentSubmissionErrorContext): Promise<void> {
    // Track as API error
    trackError.api(error, '/api/skills/assessment', 'POST', 400);

    // Track with assessment context
    errorTracker.captureException(error, {
      tags: {
        errorType: 'assessment_submission',
        assessmentType: context.assessmentData.assessmentType,
        skillCount: context.assessmentData.skillCount.toString(),
        hasValidationErrors: context.validationErrors ? 'true' : 'false',
      },
      extra: {
        userId: context.userId,
        assessmentData: context.assessmentData,
        validationErrors: context.validationErrors,
        submissionAttempt: context.submissionAttempt,
        userExperience: 'poor',
      },
      user: {
        id: context.userId,
      },
    });
  }

  /**
   * Track assessment validation errors
   */
  async trackAssessmentValidationError(error: Error, context: AssessmentValidationErrorContext): Promise<void> {
    // Track as validation error
    trackError.validation(error, context.field, context.value);

    // Track with validation context
    errorTracker.captureException(error, {
      tags: {
        errorType: 'assessment_validation',
        field: context.field,
        skillName: context.skillName,
        validationType: 'range_check',
      },
      extra: {
        userId: context.userId,
        field: context.field,
        value: context.value,
        expectedRange: context.expectedRange,
        skillName: context.skillName,
        dataQualityImpact: 'high',
      },
      user: {
        id: context.userId,
      },
    });
  }

  /**
   * Track AI service errors
   */
  async trackAIServiceError(error: Error, context: AIServiceErrorContext): Promise<void> {
    errorTracker.captureException(error, {
      tags: {
        errorType: 'ai_service',
        aiProvider: context.aiProvider,
        analysisType: context.analysisRequest.analysisType,
        errorCategory: 'rate_limit',
        severity: 'high',
      },
      extra: {
        userId: context.userId,
        analysisRequest: context.analysisRequest,
        requestId: context.requestId,
        rateLimitInfo: context.rateLimitInfo,
        retryStrategy: 'exponential_backoff',
        estimatedRetryTime: context.rateLimitInfo ? context.rateLimitInfo.resetTime : Date.now() + 60000,
      },
      user: {
        id: context.userId,
      },
    });
  }

  /**
   * Track AI response quality issues
   */
  async trackAIResponseQuality(issue: AIResponseQualityIssue): Promise<void> {
    const qualityGaps = {
      completeness: Math.round((issue.expectedQuality.completeness - issue.qualityMetrics.responseCompleteness) * 10) / 10,
      relevance: Math.round((issue.expectedQuality.relevance - issue.qualityMetrics.responseRelevance) * 10) / 10,
      accuracy: Math.round((issue.expectedQuality.accuracy - issue.qualityMetrics.responseAccuracy) * 10) / 10,
    };

    errorTracker.captureMessage('AI response quality below threshold', {
      level: 'warning',
      tags: {
        errorType: 'ai_quality',
        analysisType: issue.analysisType,
        qualityIssue: 'low_completeness',
        severity: 'medium',
      },
      extra: {
        userId: issue.userId,
        requestId: issue.requestId,
        qualityMetrics: issue.qualityMetrics,
        qualityGaps,
        improvementActions: [
          'review_prompt_engineering',
          'adjust_model_parameters',
          'enhance_context_data',
        ],
      },
    });
  }

  /**
   * Track performance issues
   */
  async trackPerformanceIssue(data: PerformanceData): Promise<void> {
    // Track as performance error
    trackError.performance(
      'Skill gap analysis exceeded performance threshold',
      'response_time',
      data.responseTime,
      data.threshold
    );

    // Track with performance context
    errorTracker.captureMessage('Performance degradation in skill gap analysis', {
      level: 'warning',
      tags: {
        errorType: 'performance',
        operation: data.operation,
        severity: 'high',
        cacheStatus: data.cacheHit ? 'hit' : 'miss',
      },
      extra: {
        userId: data.userId,
        responseTime: data.responseTime,
        threshold: data.threshold,
        exceedanceRatio: Math.round(data.responseTime / data.threshold),
        skillCount: data.skillCount,
        analysisComplexity: data.analysisComplexity,
        optimizationSuggestions: [
          'implement_result_caching',
          'optimize_ai_prompt',
          'reduce_analysis_scope',
        ],
      },
    });
  }

  /**
   * Track memory usage issues
   */
  async trackMemoryUsage(data: MemoryData): Promise<void> {
    const memoryExceedance = data.memoryUsage - data.memoryLimit;
    const memoryPerAssessment = data.assessmentCount ? data.memoryUsage / data.assessmentCount : 0;

    errorTracker.captureMessage('Memory usage exceeded limit during skill assessment processing', {
      level: 'error',
      tags: {
        errorType: 'memory_usage',
        operation: data.operation,
        severity: 'high',
      },
      extra: {
        userId: data.userId,
        memoryUsage: data.memoryUsage,
        memoryLimit: data.memoryLimit,
        memoryExceedance,
        assessmentCount: data.assessmentCount,
        processingTime: data.processingTime,
        memoryPerAssessment,
        recommendations: [
          'implement_streaming_processing',
          'reduce_batch_size',
          'optimize_data_structures',
        ],
      },
    });
  }

  /**
   * Track user workflow abandonment
   */
  async trackUserWorkflowAbandonment(data: UserWorkflowAbandonmentData): Promise<void> {
    const possibleCauses = [];
    const improvementSuggestions = [];

    if (data.sessionData.connectionSpeed === 'slow') {
      possibleCauses.push('slow_connection_on_mobile');
      improvementSuggestions.push('optimize_mobile_experience');
    }

    if (data.completionPercentage < 50) {
      possibleCauses.push('complex_assessment_form', 'unclear_instructions');
      improvementSuggestions.push('add_progress_indicators', 'implement_auto_save');
    }

    errorTracker.captureMessage('User abandoned skill gap analysis workflow', {
      level: 'info',
      tags: {
        errorType: 'user_abandonment',
        workflowType: data.workflowType,
        abandonmentStage: data.abandonmentStage,
        deviceType: data.sessionData.deviceType,
        uxImpact: 'high',
      },
      extra: {
        userId: data.userId,
        timeSpent: data.timeSpent,
        completionPercentage: data.completionPercentage,
        lastAction: data.lastAction,
        sessionData: data.sessionData,
        possibleCauses,
        improvementSuggestions,
      },
    });
  }

  /**
   * Track form validation frustration
   */
  async trackFormValidationFrustration(data: FormValidationFrustrationData): Promise<void> {
    const averageTimePerAttempt = data.timeSpent / data.totalAttempts;

    errorTracker.captureMessage('User experiencing form validation frustration', {
      level: 'warning',
      tags: {
        errorType: 'ux_frustration',
        formType: data.formType,
        frustrationLevel: 'high',
        validationIssues: 'multiple',
      },
      extra: {
        userId: data.userId,
        validationErrors: data.validationErrors,
        totalAttempts: data.totalAttempts,
        timeSpent: data.timeSpent,
        averageTimePerAttempt,
        uxImprovements: [
          'add_inline_validation_hints',
          'improve_error_messages',
          'add_field_examples',
        ],
      },
    });
  }

  /**
   * Add workflow breadcrumb
   */
  async addWorkflowBreadcrumb(step: WorkflowStep): Promise<void> {
    const message = step.step.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

    ErrorReporter.addBreadcrumb(message, 'skill_gap_workflow', {
      userId: step.userId,
      step: step.step,
      ...step.data,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Set user context
   */
  async setUserContext(context: UserContext): Promise<void> {
    errorTracker.setUser({
      id: context.userId,
      email: context.email,
    });

    ErrorReporter.addBreadcrumb('User context set for skill gap analysis', 'user_context', {
      skillLevel: context.skillLevel,
      careerPath: context.careerPath,
      analysisHistory: context.analysisHistory,
    });
  }

  /**
   * Generate error summary report
   */
  async generateErrorSummary(timeRange: TimeRange): Promise<ErrorSummary> {
    // This would typically query Sentry API or local error logs
    // For now, return mock data structure
    return {
      timeRange,
      totalErrors: 0,
      errorsByType: {},
      topErrors: [],
      affectedUsers: 0,
      performanceIssues: 0,
      qualityIssues: 0,
      recommendations: [],
      trends: {},
    };
  }

  /**
   * Analyze error patterns
   */
  async analyzeErrorPatterns(): Promise<ErrorPatternAnalysis> {
    // This would typically analyze error data for patterns
    // For now, return mock data structure
    return {
      commonErrorPatterns: [],
      userSegmentImpacts: {},
      timeBasedTrends: {},
      correlationInsights: [],
      preventionStrategies: [],
      priorityActions: [],
    };
  }
}
